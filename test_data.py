import pandas as pd
import numpy as np

# Test data loading
print("Testing data loading...")
try:
    df = pd.read_csv('Nifty.csv')
    print(f"✅ Data loaded successfully!")
    print(f"📊 Shape: {df.shape}")
    print(f"📅 Columns: {list(df.columns)}")
    print(f"📈 First few rows:")
    print(df.head())
    print(f"📉 Data types:")
    print(df.dtypes)
    
    # Check for datetime conversion
    df['datetime'] = pd.to_datetime(df['datetime'])
    print(f"✅ Datetime conversion successful!")
    print(f"📅 Date range: {df['datetime'].min()} to {df['datetime'].max()}")
    
except Exception as e:
    print(f"❌ Error: {e}")
