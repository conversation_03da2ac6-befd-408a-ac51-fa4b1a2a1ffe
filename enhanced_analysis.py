#!/usr/bin/env python3
"""
Enhanced Analysis with Custom Rules Integration
Combines the original Pine Script analysis with your custom trading instructions
"""

from simple_analysis import PriceActionAnalyzer
from my_custom_rules import get_my_custom_rules, my_custom_signal_filter, my_custom_entry_logic
import pandas as pd
import numpy as np

class EnhancedAnalyzer(PriceActionAnalyzer):
    def __init__(self, data_file, use_custom_rules=True):
        """Initialize with custom rules integration"""
        super().__init__(data_file)
        
        self.use_custom_rules = use_custom_rules
        self.custom_rules = get_my_custom_rules() if use_custom_rules else {}
        
        # Additional tracking
        self.raw_signals = []
        self.filtered_signals = []
        self.rejected_signals = []
        
        print(f"🔧 Custom Rules: {'ENABLED' if use_custom_rules else 'DISABLED'}")
    
    def apply_custom_filters(self, signals):
        """Apply custom filtering rules to signals"""
        if not self.use_custom_rules:
            return signals, []
        
        filtered = []
        rejected = []
        
        print(f"\n🔍 Applying custom filters to {len(signals)} signals...")
        
        for signal in signals:
            # Apply time-based filters
            if not self._check_time_filters(signal):
                rejected.append({**signal, 'rejection_reason': 'Time-based filter'})
                continue
            
            # Apply signal strength filters
            if not self._check_signal_strength(signal):
                rejected.append({**signal, 'rejection_reason': 'Signal strength filter'})
                continue
            
            # Apply risk-reward filters
            if not self._check_risk_reward(signal):
                rejected.append({**signal, 'rejection_reason': 'Risk-reward filter'})
                continue
            
            # Apply custom user logic
            should_take, reason = my_custom_signal_filter(signal, self.df)
            if not should_take:
                rejected.append({**signal, 'rejection_reason': f'Custom filter: {reason}'})
                continue
            
            # Signal passed all filters
            enhanced_signal = self._enhance_signal(signal)
            filtered.append(enhanced_signal)
        
        print(f"✅ Signals passed filters: {len(filtered)}")
        print(f"❌ Signals rejected: {len(rejected)}")
        
        return filtered, rejected
    
    def _check_time_filters(self, signal):
        """Check time-based filtering rules"""
        if not self.custom_rules.get('signal_filters', {}).get('avoid_first_hour', False):
            return True
        
        hour = signal['time'].hour
        minute = signal['time'].minute
        
        # Avoid first hour (9:15-10:15)
        if hour == 9 and minute >= 15:
            return False
        if hour == 10 and minute <= 15:
            return False
        
        # Avoid last hour (14:30-15:30)
        if self.custom_rules.get('signal_filters', {}).get('avoid_last_hour', False):
            if hour == 14 and minute >= 30:
                return False
            if hour == 15 and minute <= 30:
                return False
        
        return True
    
    def _check_signal_strength(self, signal):
        """Check signal strength requirements"""
        filters = self.custom_rules.get('signal_filters', {})
        
        # Check if swing confirmation is required
        if filters.get('require_swing_confirmation', False):
            if 'Internal' in signal['type']:
                return False
        
        return True
    
    def _check_risk_reward(self, signal):
        """Check risk-reward requirements"""
        entry_conditions = self.custom_rules.get('entry_conditions', {})
        min_rr = entry_conditions.get('min_risk_reward', 1.0)
        
        # Calculate approximate RR (simplified)
        # In a real implementation, you'd calculate this more precisely
        estimated_rr = 1.8  # Placeholder - would calculate based on levels
        
        return estimated_rr >= min_rr
    
    def _enhance_signal(self, signal):
        """Enhance signal with additional information"""
        enhanced = signal.copy()
        
        # Add position sizing
        risk_mgmt = self.custom_rules.get('risk_management', {})
        max_risk = risk_mgmt.get('max_risk_per_trade', 0.02)
        
        # Calculate position size (simplified)
        account_size = 100000  # Default account size
        risk_amount = account_size * max_risk
        stop_distance = 50  # Simplified stop distance
        
        enhanced['position_size'] = int(risk_amount / stop_distance)
        enhanced['risk_amount'] = risk_amount
        
        # Add confidence adjustment
        if risk_mgmt.get('position_size_by_confidence', False):
            confidence_multiplier = {
                'High': 1.0,
                'Medium': 0.7,
                'Low': 0.5
            }.get(enhanced.get('confidence', 'Medium'), 0.7)
            
            enhanced['position_size'] = int(enhanced['position_size'] * confidence_multiplier)
        
        # Apply custom entry logic
        enhanced = my_custom_entry_logic(enhanced, self.df)
        
        return enhanced
    
    def analyze_with_custom_rules(self):
        """Run analysis with custom rules applied"""
        print("🚀 Starting Enhanced Analysis with Custom Rules")
        print("-" * 60)
        
        # Run base analysis
        base_signals, order_blocks, fvgs = self.analyze()
        self.raw_signals = base_signals.copy()
        
        # Apply custom filters
        self.filtered_signals, self.rejected_signals = self.apply_custom_filters(base_signals)
        
        return self.filtered_signals, self.rejected_signals, order_blocks, fvgs
    
    def print_enhanced_summary(self):
        """Print enhanced summary with custom rule results"""
        print("\n" + "="*80)
        print("🔧 ENHANCED ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"\n📊 SIGNAL PROCESSING RESULTS:")
        print(f"   Raw Signals Generated: {len(self.raw_signals)}")
        print(f"   Signals Passed Filters: {len(self.filtered_signals)}")
        print(f"   Signals Rejected: {len(self.rejected_signals)}")
        print(f"   Filter Success Rate: {(len(self.filtered_signals)/len(self.raw_signals)*100):.1f}%" if self.raw_signals else "N/A")
        
        if self.filtered_signals:
            print(f"\n✅ QUALIFIED TRADING OPPORTUNITIES:")
            print("-" * 60)
            for i, signal in enumerate(self.filtered_signals, 1):
                print(f"{i}. {signal['type']} | ₹{signal['price']:.2f} | "
                      f"Size: {signal.get('position_size', 'N/A')} | "
                      f"Risk: ₹{signal.get('risk_amount', 'N/A'):.0f} | "
                      f"{signal['time'].strftime('%m/%d %H:%M')}")
        
        if self.rejected_signals:
            print(f"\n❌ REJECTED SIGNALS (Last 5):")
            print("-" * 60)
            for i, signal in enumerate(self.rejected_signals[-5:], 1):
                print(f"{i}. {signal['type']} | ₹{signal['price']:.2f} | "
                      f"Rejected: {signal['rejection_reason']} | "
                      f"{signal['time'].strftime('%m/%d %H:%M')}")
        
        # Show active custom rules
        print(f"\n🔧 ACTIVE CUSTOM RULES:")
        print("-" * 60)
        for category, rules in self.custom_rules.items():
            active_rules = [k for k, v in rules.items() if v is True]
            if active_rules:
                print(f"{category.upper()}: {', '.join(active_rules[:3])}{'...' if len(active_rules) > 3 else ''}")
        
        print("\n" + "="*80)
    
    def get_enhanced_recommendations(self):
        """Get trading recommendations with custom rules applied"""
        recommendations = []
        
        for signal in self.filtered_signals[-3:]:  # Last 3 qualified signals
            rec = {
                'type': signal['entry_type'],
                'strategy': 'Enhanced ' + signal.get('strategy', 'Market Structure'),
                'entry_price': signal['price'],
                'position_size': signal.get('position_size', 'Calculate based on risk'),
                'risk_amount': signal.get('risk_amount', 'N/A'),
                'confidence': signal.get('confidence', 'Medium'),
                'time': signal['time'],
                'reason': f"Passed all custom filters - {signal['type']}"
            }
            recommendations.append(rec)
        
        return recommendations

def main():
    """Main execution with custom rules"""
    try:
        print("🚀 Enhanced Nifty 50 Analysis with Custom Rules")
        print("="*60)
        
        # Initialize enhanced analyzer
        analyzer = EnhancedAnalyzer('Nifty.csv', use_custom_rules=True)
        
        # Run enhanced analysis
        filtered_signals, rejected_signals, order_blocks, fvgs = analyzer.analyze_with_custom_rules()
        
        # Print enhanced summary
        analyzer.print_enhanced_summary()
        
        # Get enhanced recommendations
        recommendations = analyzer.get_enhanced_recommendations()
        
        print(f"\n🎯 ENHANCED TRADING RECOMMENDATIONS:")
        print("="*80)
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"\n{i}. {rec['type']} OPPORTUNITY")
                print(f"   Strategy: {rec['strategy']}")
                print(f"   Entry Price: ₹{rec['entry_price']:.2f}")
                print(f"   Position Size: {rec['position_size']}")
                print(f"   Risk Amount: ₹{rec['risk_amount']:.0f}" if isinstance(rec['risk_amount'], (int, float)) else f"   Risk Amount: {rec['risk_amount']}")
                print(f"   Confidence: {rec['confidence']}")
                print(f"   Time: {rec['time'].strftime('%Y-%m-%d %H:%M')}")
                print(f"   Reason: {rec['reason']}")
        else:
            print("❌ No qualified trading opportunities found with current custom rules.")
            print("💡 Consider adjusting your custom filters in 'my_custom_rules.py'")
        
        print(f"\n💡 NEXT STEPS:")
        print("1. Review and adjust custom rules in 'my_custom_rules.py'")
        print("2. Backtest the enhanced strategy with historical data")
        print("3. Paper trade the recommendations before using real money")
        print("4. Monitor performance and refine rules based on results")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
