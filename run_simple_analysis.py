#!/usr/bin/env python3
"""
Nifty 50 Price Action Analysis
Based on <PERSON> Script: "Price Action Concepts"

This script analyzes Nifty 50 5-minute data according to the provided Pine Script
to identify optimal entry and exit points for trading.
"""

from simple_analysis import PriceActionAnalyzer
import pandas as pd
import sys

def main():
    print("🚀 Starting Nifty 50 Price Action Analysis")
    print("📋 Based on Pine Script: 'Price Action Concepts'")
    print("-" * 60)
    
    try:
        # Initialize analyzer
        analyzer = PriceActionAnalyzer('Nifty.csv')
        
        # Run complete analysis
        signals, order_blocks, fvgs = analyzer.analyze()
        
        # Print detailed summary
        analyzer.print_summary()
        
        # Generate and display trading recommendations
        recommendations = analyzer.get_trading_recommendations(recent_days=7)
        
        print("\n🎯 DETAILED TRADING RECOMMENDATIONS")
        print("="*80)
        
        if not recommendations:
            print("❌ No current trading opportunities found based on the strategy.")
            print("💡 Wait for clear market structure breaks or order block retests.")
        else:
            print(f"✅ Found {len(recommendations)} trading opportunities:")
            print()
            
            # Group recommendations by type
            buy_recs = [r for r in recommendations if r['type'] == 'BUY']
            sell_recs = [r for r in recommendations if r['type'] == 'SELL']
            
            if buy_recs:
                print("🟢 BUY OPPORTUNITIES:")
                print("-" * 40)
                for i, rec in enumerate(buy_recs, 1):
                    print(f"{i}. Strategy: {rec['strategy']}")
                    print(f"   Entry Price: ₹{rec['entry_price']:.2f}")
                    if 'target' in rec:
                        print(f"   Target: ₹{rec['target']:.2f}")
                        profit_potential = ((rec['target'] - rec['entry_price']) / rec['entry_price']) * 100
                        print(f"   Profit Potential: {profit_potential:.2f}%")
                    print(f"   Confidence: {rec['confidence']}")
                    print(f"   Reason: {rec['reason']}")
                    print(f"   Signal Time: {rec['time'].strftime('%Y-%m-%d %H:%M')}")
                    print()
            
            if sell_recs:
                print("🔴 SELL OPPORTUNITIES:")
                print("-" * 40)
                for i, rec in enumerate(sell_recs, 1):
                    print(f"{i}. Strategy: {rec['strategy']}")
                    print(f"   Entry Price: ₹{rec['entry_price']:.2f}")
                    if 'target' in rec:
                        print(f"   Target: ₹{rec['target']:.2f}")
                        profit_potential = ((rec['entry_price'] - rec['target']) / rec['entry_price']) * 100
                        print(f"   Profit Potential: {profit_potential:.2f}%")
                    print(f"   Confidence: {rec['confidence']}")
                    print(f"   Reason: {rec['reason']}")
                    print(f"   Signal Time: {rec['time'].strftime('%Y-%m-%d %H:%M')}")
                    print()
        
        # Key levels analysis
        print("📊 KEY LEVELS ANALYSIS")
        print("="*80)
        
        current_price = analyzer.df.iloc[-1]['close']
        print(f"💰 Current Nifty Price: ₹{current_price:.2f}")
        
        # Find nearest order blocks
        recent_obs = [ob for ob in order_blocks if ob['time'] >= analyzer.df['datetime'].max() - pd.Timedelta(days=30)]
        
        support_levels = []
        resistance_levels = []
        
        for ob in recent_obs:
            if ob['direction'] == 'Bullish' and ob['top'] < current_price:
                support_levels.append(ob['bottom'])
            elif ob['direction'] == 'Bearish' and ob['bottom'] > current_price:
                resistance_levels.append(ob['top'])
        
        if support_levels:
            nearest_support = max(support_levels)
            print(f"🟢 Nearest Support (Order Block): ₹{nearest_support:.2f}")
        
        if resistance_levels:
            nearest_resistance = min(resistance_levels)
            print(f"🔴 Nearest Resistance (Order Block): ₹{nearest_resistance:.2f}")
        
        # Show recent signals in detail
        print("\n📈 RECENT SIGNALS BREAKDOWN")
        print("="*80)
        
        recent_signals = [s for s in signals if s['time'] >= analyzer.df['datetime'].max() - pd.Timedelta(days=3)]
        
        if recent_signals:
            print("Last 3 days signals:")
            for signal in recent_signals[-10:]:
                direction_emoji = "🟢" if signal['direction'] == 'Bullish' else "🔴"
                print(f"{direction_emoji} {signal['time'].strftime('%m/%d %H:%M')} | "
                      f"{signal['type']} | ₹{signal['price']:.2f} | {signal['entry_type']}")
        else:
            print("No signals in the last 3 days")
        
        # Risk management guidelines
        print("\n⚠️  RISK MANAGEMENT GUIDELINES")
        print("="*80)
        print("1. 📏 Position Size: Risk only 1-2% of capital per trade")
        print("2. 🛑 Stop Loss: Place stops beyond order block boundaries")
        print("3. 🎯 Take Profit: Target next significant level or 1:2 RR minimum")
        print("4. ⏰ Time Frame: Confirm signals on higher timeframes")
        print("5. 📈 Trend: Trade with the overall market structure bias")
        print("6. 🔄 Multiple Confirmations: Wait for confluence of signals")
        
        print("\n✅ Analysis Complete!")
        print("💡 Remember: This analysis is based on historical data.")
        print("🚨 Always combine with your own analysis and risk management!")
        
    except FileNotFoundError:
        print("❌ Error: 'Nifty.csv' file not found!")
        print("📁 Please ensure the Nifty 50 data file is in the current directory.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
