#!/usr/bin/env python3
"""
YOUR CUSTOM TRADING RULES
Modify this file to add your specific trading instructions and preferences
"""

def get_my_custom_rules():
    """
    Define your custom trading rules here
    Modify each section based on your trading experience and preferences
    """
    
    return {
        # ==========================================
        # SIGNAL QUALITY FILTERS
        # ==========================================
        'signal_filters': {
            # Volume Requirements
            'min_volume_multiplier': 1.2,     # Minimum volume vs 20-period average
            
            # Time-based Filters
            'avoid_first_hour': True,          # Skip first hour (9:15-10:15)
            'avoid_last_hour': True,           # Skip last hour (14:30-15:30)
            'preferred_sessions': ['regular'], # 'opening', 'regular', 'closing'
            
            # Signal Strength Requirements
            'require_swing_confirmation': False,  # Only take swing-level signals?
            'min_candles_since_major_level': 5,   # Distance from major S/R levels
            'allow_internal_signals': True,       # Allow internal timeframe signals?
            
            # Market Condition Filters
            'avoid_choppy_markets': True,      # Skip when market is ranging
            'min_trend_strength': 0.6,        # Minimum trend strength (0-1)
            'max_volatility_threshold': 2.0,  # Skip if volatility too high
        },
        
        # ==========================================
        # ENTRY CONDITIONS
        # ==========================================
        'entry_conditions': {
            # Confirmation Requirements
            'require_retest': True,            # Wait for retest of broken level?
            'max_retest_distance': 10,         # Max points for valid retest
            'require_rejection_candle': True,  # Need rejection candle at entry?
            'min_confirmation_candles': 1,     # Min candles for confirmation
            
            # Risk-Reward Requirements
            'min_risk_reward': 1.5,            # Minimum RR ratio to take trade
            'max_stop_distance': 100,          # Max stop loss distance (points)
            
            # Multiple Timeframe Confirmation
            'check_higher_timeframes': True,   # Check 15m, 1H, 4H alignment?
            'require_htf_alignment': False,    # Must all HTFs agree?
            
            # Additional Confluence
            'require_order_block_confluence': False,  # Need OB confluence?
            'require_fvg_confluence': False,          # Need FVG confluence?
        },
        
        # ==========================================
        # RISK MANAGEMENT
        # ==========================================
        'risk_management': {
            # Position Sizing
            'max_risk_per_trade': 0.02,       # 2% max risk per trade
            'position_size_by_confidence': True,  # Adjust size by signal quality?
            
            # Stop Loss Strategy
            'stop_loss_method': 'order_block',  # 'fixed', 'atr', 'order_block', 'structure'
            'stop_buffer_points': 5,           # Extra buffer beyond stop level
            'use_trailing_stops': True,        # Use trailing stops?
            'trail_after_1R': True,            # Start trailing after 1:1 RR?
            
            # Profit Taking
            'partial_profit_at_1R': True,      # Take 50% profit at 1:1?
            'partial_profit_percentage': 0.5,  # How much to close at 1R
            'max_trade_duration_hours': 24,    # Max time to hold trade
            
            # Risk Scaling
            'reduce_size_after_loss': True,    # Reduce size after losing trade?
            'increase_size_after_wins': False, # Increase size after winning streak?
        },
        
        # ==========================================
        # MARKET CONTEXT ANALYSIS
        # ==========================================
        'market_context': {
            # Market Session Preferences
            'preferred_trading_hours': {
                'start': '10:15',              # Preferred start time
                'end': '14:30',                # Preferred end time
            },
            
            # News and Events
            'avoid_news_times': True,          # Avoid major news releases?
            'news_buffer_minutes': 30,        # Minutes before/after news to avoid
            
            # Market Volatility
            'check_volatility': True,          # Check if volatility is normal?
            'volatility_lookback': 20,        # Periods for volatility calculation
            
            # Trend Analysis
            'consider_daily_trend': True,      # Consider daily trend direction?
            'consider_weekly_trend': False,    # Consider weekly trend?
            'trend_alignment_required': False, # Must trade with trend?
        },
        
        # ==========================================
        # NIFTY 50 SPECIFIC RULES
        # ==========================================
        'nifty_specific': {
            # Index Characteristics
            'consider_sector_rotation': False,  # Check sector performance?
            'check_bank_nifty_correlation': False,  # Check Bank Nifty alignment?
            'consider_vix_levels': False,      # Check VIX for market fear?
            
            # Support/Resistance Levels
            'respect_round_numbers': True,     # Give extra weight to round numbers?
            'round_number_buffer': 50,         # Buffer around round numbers
            
            # Market Microstructure
            'consider_fii_dii_flows': False,   # Check FII/DII activity?
            'check_options_data': False,       # Consider options OI data?
        },
        
        # ==========================================
        # YOUR CUSTOM FILTERS
        # ==========================================
        'custom_filters': {
            # Add your own specific rules here
            'my_special_rule_1': True,
            'my_special_rule_2': False,
            
            # Example: Your own pattern recognition
            'avoid_friday_trades': False,      # Skip Friday trades?
            'only_trend_following': False,     # Only trade with major trend?
            'require_volume_spike': False,     # Need volume confirmation?
            
            # Your risk preferences
            'conservative_mode': False,        # More conservative settings?
            'aggressive_mode': False,          # More aggressive settings?
        },
        
        # ==========================================
        # BACKTESTING PREFERENCES
        # ==========================================
        'backtesting': {
            'commission_per_trade': 20,        # Commission cost per trade
            'slippage_points': 2,              # Expected slippage in points
            'min_trades_for_stats': 30,        # Min trades for reliable statistics
        }
    }

# ==========================================
# CUSTOM LOGIC FUNCTIONS
# ==========================================

def my_custom_signal_filter(signal, market_data):
    """
    Add your own custom signal filtering logic here
    
    Args:
        signal: The trading signal dictionary
        market_data: DataFrame with market data
    
    Returns:
        tuple: (should_take_trade: bool, reason: str)
    """
    
    # Example custom logic:
    # if signal['price'] > 25000:
    #     return False, "Price too high for my comfort"
    
    # if signal['time'].hour < 10:
    #     return False, "Too early in the session"
    
    # Add your own conditions here
    return True, "Passed custom filters"

def my_custom_entry_logic(signal, market_data):
    """
    Add your own entry timing logic here
    
    Args:
        signal: The trading signal dictionary
        market_data: DataFrame with market data
    
    Returns:
        dict: Modified signal with entry adjustments
    """
    
    # Example: Adjust entry price based on your logic
    # signal['entry_price'] = signal['price'] + 5  # Add buffer
    
    return signal

def my_custom_exit_logic(signal, current_price, market_data):
    """
    Add your own exit logic here
    
    Args:
        signal: The original signal
        current_price: Current market price
        market_data: DataFrame with market data
    
    Returns:
        dict: Exit decision {'action': 'hold/exit', 'reason': 'string'}
    """
    
    # Example custom exit logic
    # if current_price > signal['entry_price'] * 1.02:  # 2% profit
    #     return {'action': 'exit', 'reason': 'Custom profit target reached'}
    
    return {'action': 'hold', 'reason': 'Continue holding'}

# ==========================================
# INSTRUCTIONS FOR USE
# ==========================================
"""
HOW TO USE THIS FILE:

1. MODIFY THE RULES ABOVE:
   - Change the values in get_my_custom_rules() to match your preferences
   - Set True/False for features you want to enable/disable
   - Adjust numerical values to your risk tolerance

2. ADD CUSTOM LOGIC:
   - Implement your own filtering logic in my_custom_signal_filter()
   - Add entry timing adjustments in my_custom_entry_logic()
   - Define exit conditions in my_custom_exit_logic()

3. EXAMPLES OF WHAT YOU CAN ADD:
   - "Only trade after 10:30 AM"
   - "Avoid trades on expiry days"
   - "Require 2% minimum profit potential"
   - "Exit if trade goes against me for 2 hours"
   - "Only trade when Bank Nifty is also bullish"
   - "Avoid trades near round numbers like 24000, 24500"

4. INTEGRATION:
   - The main analysis script will use these rules
   - Your custom functions will be called during analysis
   - All trades will be filtered through your criteria

5. TESTING:
   - Start with conservative settings
   - Backtest with historical data
   - Gradually adjust based on results
"""

if __name__ == "__main__":
    # Test your custom rules
    rules = get_my_custom_rules()
    print("🔧 Your Custom Trading Rules:")
    for category, settings in rules.items():
        print(f"\n{category.upper()}:")
        for key, value in settings.items():
            print(f"  {key}: {value}")
