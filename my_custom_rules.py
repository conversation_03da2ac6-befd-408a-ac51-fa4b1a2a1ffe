#!/usr/bin/env python3
"""
YOUR CUSTOM TRADING RULES
Modify this file to add your specific trading instructions and preferences
"""

def get_my_custom_rules():
    """
    YOUR SPECIFIC NIFTY 50 TRADING RULES
    Based on your market experience and observations
    """

    return {
        # ==========================================
        # YOUR SPECIFIC SIGNAL FILTERS
        # ==========================================
        'signal_filters': {
            # Time-based Filters (Your Big Move Times)
            'big_move_times': ['13:00', '13:15', '13:30', '14:00', '14:30', '14:56'],
            'prefer_big_move_times': True,     # Prioritize signals at these times
            'avoid_non_move_times': False,     # Still allow other times but lower confidence

            # Market Opening Analysis
            'analyze_first_candle': True,      # First candle shows day sentiment
            'first_candle_importance': 'high', # Weight given to opening sentiment

            # Move Timing Patterns
            'moves_start_on_intervals': True,  # Moves start on :00 or :30
            'interval_buffer_minutes': 5,     # Allow 5min buffer around intervals

            # Expiry Day Rules
            'expiry_day_volatility': 'high',  # Expect high volatility on expiry
            'expiry_day_caution': True,       # Be more cautious on expiry days

            # Your Move Expectations
            'bullish_move_expectation': 140,  # Points expected in bullish moves
            'bearish_move_expectation': 200,  # Points expected in bearish moves
        },
        
        # ==========================================
        # YOUR SPECIFIC ENTRY CONDITIONS
        # ==========================================
        'entry_conditions': {
            # EQH/EQL Rules
            'analyze_eqh_eql': True,           # Look for Equal Highs/Lows
            'eqh_bearish_bias': True,          # EQH usually leads to bearish moves
            'eql_bullish_bias': True,          # EQL usually leads to bullish moves
            'eqh_eql_risk_reward': '5:10',     # 5 point risk, 10 point reward for EQH/EQL

            # Gap Analysis (Critical for your strategy)
            'analyze_gap_opening': True,       # Analyze gap up/down at opening
            'gap_up_bullish_bias': True,       # Gap up = look for bullish trades
            'gap_down_bearish_bias': True,     # Gap down = look for bearish trades
            'gap_sideways_both_sides': True,   # Sometimes gap leads to both side trades

            # Order Block & FVG Confluence
            'bullish_ob_fvg_buy': True,        # Buy when price at bullish OB/FVG
            'bearish_ob_fvg_sell': True,       # Sell when price at bearish OB/FVG
            'require_ob_fvg_confluence': True, # Need OB + FVG together

            # Support/Resistance Behavior
            'sr_interchange_common': True,     # S/R levels interchange frequently
            'respect_sr_levels': True,         # Give weight to S/R levels
        },
        
        # ==========================================
        # YOUR SPECIFIC RISK MANAGEMENT
        # ==========================================
        'risk_management': {
            # Your Specific Risk-Reward System
            'standard_risk_points': 10,        # Your standard 10 point risk
            'standard_reward_points': 20,      # Your standard 20 point reward
            'eqh_eql_risk_points': 5,          # 5 point risk for EQH/EQL
            'eqh_eql_reward_points': 10,       # 10 point reward for EQH/EQL
            'avoid_1_2_rr': True,              # Don't use 1:2 RR (doesn't match your model)

            # NEW: Advanced Trailing Stop Rules
            'use_advanced_trailing': True,     # Enable your new trailing system

            # For 10:20 RR trades
            'standard_initial_target': 20,     # Initial 20 point target
            'standard_trail_start': 20,        # Start trailing at 20 points
            'standard_trail_to': 60,           # Trail up to 60 points
            'standard_trail_step': 2,          # Trail by 2 points after 60

            # For 5:10 RR trades (EQH/EQL)
            'eqh_eql_initial_target': 10,      # Initial 10 point target
            'eqh_eql_trail_start': 10,         # Start trailing at 10 points
            'eqh_eql_trail_trigger': 5,        # Trail when 5 more points come
            'eqh_eql_trail_step': 2,           # Trail by 2 points

            # Position Sizing
            'max_risk_per_trade': 0.02,       # 2% max risk per trade
            'position_size_by_move_type': True, # Adjust size by expected move

            # Your Move-based Expectations
            'bullish_move_target': 140,        # Target 140 points in bullish moves
            'bearish_move_target': 200,        # Target 200 points in bearish moves
            'adjust_targets_by_move': True,    # Adjust targets based on move type

            # Volatility-based Adjustments
            'expiry_day_smaller_size': True,   # Smaller positions on expiry days
            'volatile_market_both_sides': True, # Trade both sides in volatile conditions
        },
        
        # ==========================================
        # MARKET CONTEXT ANALYSIS
        # ==========================================
        'market_context': {
            # Market Session Preferences
            'preferred_trading_hours': {
                'start': '10:15',              # Preferred start time
                'end': '14:30',                # Preferred end time
            },

            # News and Events
            'avoid_news_times': True,          # Avoid major news releases?
            'news_buffer_minutes': 30,        # Minutes before/after news to avoid

            # Market Volatility
            'check_volatility': True,          # Check if volatility is normal?
            'volatility_lookback': 20,        # Periods for volatility calculation

            # NEW: Gap Continuation Rules
            'gap_continuation_analysis': True,  # Enable gap continuation tracking
            'gap_continuation_days': 5,        # Track gap pattern for 5 days
            'gap_character_change': True,      # Watch for character changes

            # Trend Analysis
            'consider_daily_trend': True,      # Consider daily trend direction?
            'consider_weekly_trend': False,    # Consider weekly trend?
            'trend_alignment_required': False, # Must trade with trend?
        },
        
        # ==========================================
        # NIFTY 50 SPECIFIC RULES
        # ==========================================
        'nifty_specific': {
            # Index Characteristics
            'consider_sector_rotation': False,  # Check sector performance?
            'check_bank_nifty_correlation': False,  # Check Bank Nifty alignment?
            'consider_vix_levels': False,      # Check VIX for market fear?
            
            # Support/Resistance Levels
            'respect_round_numbers': True,     # Give extra weight to round numbers?
            'round_number_buffer': 50,         # Buffer around round numbers
            
            # Market Microstructure
            'consider_fii_dii_flows': False,   # Check FII/DII activity?
            'check_options_data': False,       # Consider options OI data?
        },
        
        # ==========================================
        # YOUR CUSTOM FILTERS
        # ==========================================
        'custom_filters': {
            # Add your own specific rules here
            'my_special_rule_1': True,
            'my_special_rule_2': False,
            
            # Example: Your own pattern recognition
            'avoid_friday_trades': False,      # Skip Friday trades?
            'only_trend_following': False,     # Only trade with major trend?
            'require_volume_spike': False,     # Need volume confirmation?
            
            # Your risk preferences
            'conservative_mode': False,        # More conservative settings?
            'aggressive_mode': False,          # More aggressive settings?
        },
        
        # ==========================================
        # BACKTESTING PREFERENCES
        # ==========================================
        'backtesting': {
            'commission_per_trade': 20,        # Commission cost per trade
            'slippage_points': 2,              # Expected slippage in points
            'min_trades_for_stats': 30,        # Min trades for reliable statistics
        }
    }

# ==========================================
# CUSTOM LOGIC FUNCTIONS
# ==========================================

def my_custom_signal_filter(signal, market_data):
    """
    YOUR SPECIFIC SIGNAL FILTERING LOGIC
    Based on your Nifty 50 trading rules
    """

    signal_time = signal['time']
    signal_price = signal['price']

    # Check if signal occurs at your preferred big move times
    big_move_times = ['13:00', '13:15', '13:30', '14:00', '14:30', '14:56']
    signal_time_str = signal_time.strftime('%H:%M')

    is_big_move_time = any(abs((signal_time.hour * 60 + signal_time.minute) -
                              (int(t.split(':')[0]) * 60 + int(t.split(':')[1]))) <= 5
                          for t in big_move_times)

    # Check if move starts on :00 or :30 intervals (your observation)
    minute = signal_time.minute
    is_interval_time = minute in [0, 30] or abs(minute - 0) <= 5 or abs(minute - 30) <= 5

    # Analyze gap opening (if it's the first signal of the day)
    if signal_time.hour == 9 and signal_time.minute <= 20:
        # This is likely the opening - analyze gap
        # You would need previous day's close to calculate gap
        # For now, we'll assume gap analysis is done elsewhere
        pass

    # Your rule: Prioritize signals at big move times
    confidence_boost = 0
    if is_big_move_time:
        confidence_boost += 0.3

    if is_interval_time:
        confidence_boost += 0.2

    # Apply confidence boost to signal
    if 'confidence_score' not in signal:
        signal['confidence_score'] = 0.5  # Base confidence

    signal['confidence_score'] = min(1.0, signal['confidence_score'] + confidence_boost)

    return True, f"Enhanced confidence: {signal['confidence_score']:.2f}"

def my_custom_entry_logic(signal, market_data):
    """
    YOUR SPECIFIC ENTRY LOGIC
    Implements your risk-reward and EQH/EQL rules
    """

    # Apply your specific risk-reward system
    signal_type = signal.get('type', '')

    # Check if this is an EQH/EQL scenario
    if 'EQH' in signal_type or 'EQL' in signal_type:
        # Your rule: 5 point risk, 10 point reward for EQH/EQL
        signal['risk_points'] = 5
        signal['reward_points'] = 10
        signal['risk_reward_ratio'] = '1:2'
    else:
        # Your standard rule: 10 point risk, 20 point reward
        signal['risk_points'] = 10
        signal['reward_points'] = 20
        signal['risk_reward_ratio'] = '1:2'

    # Adjust entry based on your gap analysis
    current_time = signal['time']
    if current_time.hour == 9 and current_time.minute <= 25:
        # Opening period - apply gap analysis
        signal['apply_gap_analysis'] = True
        signal['watch_first_candle'] = True

    # Apply your move expectations
    if signal['direction'] == 'Bullish':
        signal['move_expectation'] = 140  # Your bullish move expectation
    else:
        signal['move_expectation'] = 200  # Your bearish move expectation

    return signal

def my_custom_exit_logic(signal, current_price, market_data):
    """
    YOUR ADVANCED TRAILING STOP LOGIC
    Implements your new trailing stop rules for maximum profit extraction

    Args:
        signal: The original signal with entry details
        current_price: Current market price
        market_data: DataFrame with market data

    Returns:
        dict: Exit decision with updated stop loss and action
    """

    entry_price = signal.get('entry_price', signal['price'])
    direction = signal['direction']
    trade_type = signal.get('type', '')

    # Calculate current P&L
    if direction == 'Bullish':
        current_pnl = current_price - entry_price
    else:  # Bearish
        current_pnl = entry_price - current_price

    # Determine if this is EQH/EQL (5:10) or standard (10:20) trade
    is_eqh_eql = 'EQH' in trade_type or 'EQL' in trade_type

    if is_eqh_eql:
        # EQH/EQL Trailing Logic (5:10 RR)
        initial_target = 10

        if current_pnl >= initial_target:
            # Move stop to breakeven (entry price)
            new_stop = entry_price

            if current_pnl >= (initial_target + 5):  # 5 more points after target
                # Start trailing by 2 points
                if direction == 'Bullish':
                    new_stop = current_price - 2
                else:
                    new_stop = current_price + 2

                return {
                    'action': 'update_stop',
                    'new_stop_loss': new_stop,
                    'reason': f'EQH/EQL trailing: {current_pnl:.1f}pts profit, stop at {new_stop:.2f}'
                }
            else:
                return {
                    'action': 'update_stop',
                    'new_stop_loss': new_stop,
                    'reason': f'EQH/EQL target hit: Stop moved to breakeven at {new_stop:.2f}'
                }
    else:
        # Standard Trade Trailing Logic (10:20 RR)
        initial_target = 20

        if current_pnl >= initial_target:
            # Move stop to entry price (breakeven)
            new_stop = entry_price

            if current_pnl >= 60:  # After 60 points, trail by 2 points
                if direction == 'Bullish':
                    new_stop = current_price - 2
                else:
                    new_stop = current_price + 2

                return {
                    'action': 'update_stop',
                    'new_stop_loss': new_stop,
                    'reason': f'Standard trailing: {current_pnl:.1f}pts profit, trailing by 2pts'
                }
            elif current_pnl < 60:  # Between 20-60 points, keep trailing to maximize
                if direction == 'Bullish':
                    new_stop = max(entry_price, current_price - 10)  # Trail with 10pt buffer
                else:
                    new_stop = min(entry_price, current_price + 10)

                return {
                    'action': 'update_stop',
                    'new_stop_loss': new_stop,
                    'reason': f'Standard target hit: Trailing to maximize profit ({current_pnl:.1f}pts)'
                }

    # If target not yet hit, continue holding
    return {
        'action': 'hold',
        'reason': f'Target not reached: {current_pnl:.1f}pts profit'
    }

def analyze_gap_continuation_pattern(recent_gaps, current_gap):
    """
    YOUR GAP CONTINUATION ANALYSIS
    Analyzes if gap pattern is continuing or changing character

    Args:
        recent_gaps: List of recent gap sentiments ['Bullish', 'Bearish', ...]
        current_gap: Current day's gap sentiment

    Returns:
        dict: Gap continuation analysis
    """

    if len(recent_gaps) < 2:
        return {
            'pattern': 'insufficient_data',
            'continuation_expected': False,
            'character_change': False,
            'confidence': 'low'
        }

    # Count recent gap types
    recent_bullish = recent_gaps.count('Bullish')
    recent_bearish = recent_gaps.count('Bearish')

    # Determine if we're in a gap continuation phase
    if recent_bullish >= 3:  # 3+ bullish gaps
        pattern = 'bullish_continuation'
        continuation_expected = current_gap == 'Bullish'
        character_change = current_gap == 'Bearish'
    elif recent_bearish >= 3:  # 3+ bearish gaps
        pattern = 'bearish_continuation'
        continuation_expected = current_gap == 'Bearish'
        character_change = current_gap == 'Bullish'
    else:
        pattern = 'mixed_pattern'
        continuation_expected = False
        character_change = False

    # Determine confidence based on pattern strength
    if len(recent_gaps) >= 4:
        if recent_bullish >= 4 or recent_bearish >= 4:
            confidence = 'high'
        elif recent_bullish >= 3 or recent_bearish >= 3:
            confidence = 'medium'
        else:
            confidence = 'low'
    else:
        confidence = 'low'

    return {
        'pattern': pattern,
        'continuation_expected': continuation_expected,
        'character_change': character_change,
        'confidence': confidence,
        'recent_bullish_count': recent_bullish,
        'recent_bearish_count': recent_bearish,
        'recommendation': get_gap_continuation_recommendation(pattern, current_gap, character_change)
    }

def get_gap_continuation_recommendation(pattern, current_gap, character_change):
    """
    Get trading recommendation based on gap continuation analysis
    """

    if character_change:
        return {
            'action': 'prepare_for_reversal',
            'bias': 'opposite_to_recent_trend',
            'reason': 'Gap character change detected - trend reversal likely',
            'strategy': 'Look for reversal patterns and trade in new gap direction'
        }

    if pattern == 'bullish_continuation' and current_gap == 'Bullish':
        return {
            'action': 'continue_bullish_bias',
            'bias': 'bullish',
            'reason': 'Bullish gap continuation pattern intact',
            'strategy': 'Look for EQL patterns and bullish setups'
        }

    if pattern == 'bearish_continuation' and current_gap == 'Bearish':
        return {
            'action': 'continue_bearish_bias',
            'bias': 'bearish',
            'reason': 'Bearish gap continuation pattern intact',
            'strategy': 'Look for EQH patterns and bearish setups'
        }

    return {
        'action': 'neutral',
        'bias': 'follow_daily_gap',
        'reason': 'No clear continuation pattern',
        'strategy': 'Trade based on daily gap sentiment only'
    }

# ==========================================
# INSTRUCTIONS FOR USE
# ==========================================
"""
HOW TO USE THIS FILE:

1. MODIFY THE RULES ABOVE:
   - Change the values in get_my_custom_rules() to match your preferences
   - Set True/False for features you want to enable/disable
   - Adjust numerical values to your risk tolerance

2. YOUR NEW ADVANCED FEATURES:
   - Gap Continuation Analysis: Tracks gap patterns over multiple days
   - Advanced Trailing Stops: Maximizes profits with intelligent trailing
   - Character Change Detection: Identifies trend reversals
   - Multi-day Pattern Recognition: Uses historical gap data

3. TRAILING STOP LOGIC:
   For 10:20 RR trades:
   - At 20 points: Move stop to breakeven
   - Continue trailing up to 60 points
   - After 60 points: Trail by 2 points

   For 5:10 RR trades (EQH/EQL):
   - At 10 points: Move stop to breakeven
   - When 5 more points come: Start trailing by 2 points

4. GAP CONTINUATION RULES:
   - Gap ups continue for several days until character change
   - Gap downs continue for several days until character change
   - Character change signals trend reversal
   - Use gap history to predict continuation vs reversal

5. INTEGRATION:
   - The main analysis script will use these rules
   - Your custom functions will be called during analysis
   - All trades will be filtered through your criteria
   - Advanced trailing will maximize profit extraction

6. TESTING:
   - Start with conservative settings
   - Backtest with historical data
   - Monitor trailing stop performance
   - Gradually adjust based on results
"""

if __name__ == "__main__":
    # Test your custom rules
    rules = get_my_custom_rules()
    print("🔧 Your Custom Trading Rules:")
    for category, settings in rules.items():
        print(f"\n{category.upper()}:")
        for key, value in settings.items():
            print(f"  {key}: {value}")
