#!/usr/bin/env python3
"""
Sequential Backtest Engine with Advanced Rules
TRUE SEQUENTIAL BACKTESTING - No Future Data Leakage
Each day is analyzed as if it's happening in real-time
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class SequentialBacktestEngine:
    def __init__(self, data_file):
        """Initialize with complete dataset but process sequentially"""
        # Load complete dataset
        self.complete_df = pd.read_csv(data_file, low_memory=False)
        self.complete_df['datetime'] = pd.to_datetime(self.complete_df['datetime'])
        self.complete_df = self.complete_df.sort_values('datetime').reset_index(drop=True)
        
        # Filter to start from March 10, 2025
        start_date = pd.to_datetime('2025-03-10')
        self.complete_df = self.complete_df[self.complete_df['datetime'] >= start_date].reset_index(drop=True)
        
        # Sequential processing variables
        self.current_date_index = 0
        self.processed_dates = []
        self.gap_history = []  # Build this day by day
        self.daily_reports = []
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0
        self.active_trades = []
        
        # Your advanced rules
        self.big_move_times = ['13:00', '13:15', '13:30', '14:00', '14:30', '14:56']
        
        # Get all unique trading dates
        self.all_trading_dates = sorted(self.complete_df['datetime'].dt.date.unique())
        
        print(f"🚀 Sequential Backtest Engine Initialized")
        print(f"📅 Start Date: {self.all_trading_dates[0]}")
        print(f"📅 End Date: {self.all_trading_dates[-1]}")
        print(f"📊 Total Trading Days: {len(self.all_trading_dates)}")
        print(f"📈 Total Data Points: {len(self.complete_df):,}")
        print(f"⚠️  NO FUTURE DATA WILL BE USED - TRUE SEQUENTIAL PROCESSING")
    
    def get_data_up_to_date(self, target_date):
        """Get data only up to target date (no future data leakage)"""
        return self.complete_df[self.complete_df['datetime'].dt.date <= target_date].copy()
    
    def get_daily_data(self, target_date, available_data):
        """Get data for specific day from available data only"""
        daily_data = available_data[available_data['datetime'].dt.date == target_date].copy()
        return daily_data.reset_index(drop=True) if len(daily_data) > 0 else None
    
    def analyze_gap_opening(self, daily_data):
        """Analyze gap and first candle (same as before)"""
        if len(daily_data) == 0:
            return None
        
        first_candle = daily_data.iloc[0]
        first_open = first_candle['open']
        first_close = first_candle['close']
        first_high = first_candle['high']
        first_low = first_candle['low']
        
        candle_range = first_high - first_low
        is_big_bar = candle_range > 50
        
        if first_close > first_open:
            sentiment = "Bullish"
            recommended_bias = "BUY"
        else:
            sentiment = "Bearish"
            recommended_bias = "SELL"
        
        return {
            'sentiment': sentiment,
            'recommended_bias': recommended_bias,
            'is_big_bar': is_big_bar,
            'candle_range': candle_range,
            'opening_price': first_open,
            'first_close': first_close
        }
    
    def analyze_gap_continuation(self, current_gap):
        """Analyze gap continuation using ONLY historical data"""
        if len(self.gap_history) < 2:
            return {
                'pattern': 'insufficient_data',
                'continuation_expected': False,
                'character_change': False,
                'confidence': 'low'
            }
        
        # Use only last 5 days of gap history (no future data)
        recent_gaps = self.gap_history[-5:] if len(self.gap_history) >= 5 else self.gap_history
        
        # Count recent gap types
        recent_bullish = recent_gaps.count('Bullish')
        recent_bearish = recent_gaps.count('Bearish')
        
        # Determine pattern
        if recent_bullish >= 3:
            pattern = 'bullish_continuation'
            continuation_expected = current_gap == 'Bullish'
            character_change = current_gap == 'Bearish'
        elif recent_bearish >= 3:
            pattern = 'bearish_continuation'
            continuation_expected = current_gap == 'Bearish'
            character_change = current_gap == 'Bullish'
        else:
            pattern = 'mixed_pattern'
            continuation_expected = False
            character_change = False
        
        confidence = 'high' if len(recent_gaps) >= 4 and (recent_bullish >= 4 or recent_bearish >= 4) else 'medium' if len(recent_gaps) >= 3 else 'low'
        
        return {
            'pattern': pattern,
            'continuation_expected': continuation_expected,
            'character_change': character_change,
            'confidence': confidence,
            'recent_gaps': recent_gaps.copy(),
            'recent_bullish_count': recent_bullish,
            'recent_bearish_count': recent_bearish
        }
    
    def detect_eqh_eql_patterns(self, daily_data):
        """Detect EQH/EQL patterns within the day"""
        if len(daily_data) < 20:
            return []
        
        patterns = []
        tolerance = 10
        
        for i in range(10, len(daily_data) - 5):
            current_high = daily_data.iloc[i]['high']
            current_low = daily_data.iloc[i]['low']
            current_time = daily_data.iloc[i]['datetime']
            
            # Check for equal highs
            recent_highs = daily_data.iloc[i-10:i]['high'].values
            equal_highs = [h for h in recent_highs if abs(h - current_high) <= tolerance]
            
            if len(equal_highs) >= 2:
                patterns.append({
                    'type': 'EQH',
                    'time': current_time,
                    'price': current_high,
                    'direction': 'Bearish',
                    'entry_type': 'SELL',
                    'risk_points': 5,
                    'reward_points': 10,
                    'confidence': 'High'
                })
            
            # Check for equal lows
            recent_lows = daily_data.iloc[i-10:i]['low'].values
            equal_lows = [l for l in recent_lows if abs(l - current_low) <= tolerance]
            
            if len(equal_lows) >= 2:
                patterns.append({
                    'type': 'EQL',
                    'time': current_time,
                    'price': current_low,
                    'direction': 'Bullish',
                    'entry_type': 'BUY',
                    'risk_points': 5,
                    'reward_points': 10,
                    'confidence': 'High'
                })
        
        return patterns
    
    def apply_timing_filters(self, signals):
        """Apply your timing rules"""
        filtered = []
        
        for signal in signals:
            signal_time = signal['time']
            
            # Check big move times
            is_big_move_time = False
            for move_time in self.big_move_times:
                move_hour, move_minute = map(int, move_time.split(':'))
                time_diff = abs((signal_time.hour * 60 + signal_time.minute) - 
                               (move_hour * 60 + move_minute))
                if time_diff <= 5:
                    is_big_move_time = True
                    signal['big_move_time'] = move_time
                    break
            
            # Check interval timing
            minute = signal_time.minute
            is_interval_time = minute in [0, 30] or abs(minute - 0) <= 5 or abs(minute - 30) <= 5
            
            # Calculate confidence
            confidence_score = 0.5
            if is_big_move_time:
                confidence_score += 0.3
            if is_interval_time:
                confidence_score += 0.2
            
            signal['confidence_score'] = confidence_score
            signal['is_big_move_time'] = is_big_move_time
            signal['is_interval_time'] = is_interval_time
            
            # Filter by confidence
            if confidence_score >= 0.6 or is_big_move_time:
                filtered.append(signal)
        
        return filtered
    
    def simulate_trade_with_advanced_trailing(self, signal, daily_data):
        """Simulate trade with your new advanced trailing rules"""
        entry_time = signal['time']
        entry_price = signal['price']
        direction = signal['direction']
        risk_points = signal['risk_points']
        reward_points = signal['reward_points']
        
        # Initial stop and target
        if direction == 'Bullish':
            initial_stop = entry_price - risk_points
            initial_target = entry_price + reward_points
        else:
            initial_stop = entry_price + risk_points
            initial_target = entry_price - reward_points
        
        # Get future data (only within the same day)
        future_data = daily_data[daily_data['datetime'] > entry_time]
        
        if len(future_data) == 0:
            return {
                'outcome': 'No Data',
                'exit_price': entry_price,
                'exit_time': entry_time,
                'pnl': 0,
                'exit_reason': 'End of day',
                'max_profit': 0,
                'trailing_used': False
            }
        
        # Track trade with advanced trailing
        current_stop = initial_stop
        max_profit = 0
        trailing_activated = False
        
        for _, row in future_data.iterrows():
            current_price = row['close']
            current_time = row['datetime']
            
            # Calculate P&L
            if direction == 'Bullish':
                current_pnl = current_price - entry_price
            else:
                current_pnl = entry_price - current_price
            
            max_profit = max(max_profit, current_pnl)
            
            # Apply your advanced trailing rules
            if current_pnl >= reward_points and not trailing_activated:
                # Target hit - activate trailing
                trailing_activated = True
                current_stop = entry_price  # Move to breakeven
                
                # For EQH/EQL (5:10), start trailing when 5 more points come
                if signal['type'] in ['EQH', 'EQL'] and current_pnl >= (reward_points + 5):
                    if direction == 'Bullish':
                        current_stop = current_price - 2  # Trail by 2 points
                    else:
                        current_stop = current_price + 2
                
                # For standard trades (10:20), trail up to 60 points
                elif signal['type'] not in ['EQH', 'EQL']:
                    if current_pnl >= 60:  # After 60 points, trail by 2
                        if direction == 'Bullish':
                            current_stop = current_price - 2
                        else:
                            current_stop = current_price + 2
                    else:  # Between target and 60, trail with buffer
                        if direction == 'Bullish':
                            current_stop = max(entry_price, current_price - 10)
                        else:
                            current_stop = min(entry_price, current_price + 10)
            
            # Check stop loss
            if direction == 'Bullish':
                if row['low'] <= current_stop:
                    final_pnl = current_stop - entry_price
                    return {
                        'outcome': 'Win' if final_pnl > 0 else 'Loss',
                        'exit_price': current_stop,
                        'exit_time': current_time,
                        'pnl': final_pnl,
                        'exit_reason': 'Trailing Stop' if trailing_activated else 'Stop Loss',
                        'max_profit': max_profit,
                        'trailing_used': trailing_activated
                    }
            else:
                if row['high'] >= current_stop:
                    final_pnl = entry_price - current_stop
                    return {
                        'outcome': 'Win' if final_pnl > 0 else 'Loss',
                        'exit_price': current_stop,
                        'exit_time': current_time,
                        'pnl': final_pnl,
                        'exit_reason': 'Trailing Stop' if trailing_activated else 'Stop Loss',
                        'max_profit': max_profit,
                        'trailing_used': trailing_activated
                    }
        
        # End of day
        final_price = future_data.iloc[-1]['close']
        final_pnl = final_price - entry_price if direction == 'Bullish' else entry_price - final_price
        
        return {
            'outcome': 'Win' if final_pnl > 0 else 'Loss' if final_pnl < 0 else 'Breakeven',
            'exit_price': final_price,
            'exit_time': future_data.iloc[-1]['datetime'],
            'pnl': final_pnl,
            'exit_reason': 'End of Day',
            'max_profit': max_profit,
            'trailing_used': trailing_activated
        }

    def process_single_day(self, target_date):
        """Process single day as if it's happening live (no future data)"""
        # Get data only up to this date
        available_data = self.get_data_up_to_date(target_date)
        daily_data = self.get_daily_data(target_date, available_data)

        if daily_data is None or len(daily_data) == 0:
            return None

        # Analyze gap (using only current day data)
        gap_analysis = self.analyze_gap_opening(daily_data)
        current_gap = gap_analysis['sentiment'] if gap_analysis else None

        # Analyze gap continuation (using only historical gap data)
        gap_continuation = self.analyze_gap_continuation(current_gap)

        # Add current gap to history AFTER analysis
        if current_gap:
            self.gap_history.append(current_gap)

        # Detect patterns
        eqh_eql_signals = self.detect_eqh_eql_patterns(daily_data)

        # Apply timing filters
        filtered_signals = self.apply_timing_filters(eqh_eql_signals)

        # Apply gap alignment
        aligned_signals = []
        for signal in filtered_signals:
            # Check alignment with gap and continuation pattern
            gap_aligned = self.check_gap_alignment(signal, gap_analysis, gap_continuation)
            if gap_aligned:
                signal['gap_continuation'] = gap_continuation
                signal['gap_aligned'] = True
                aligned_signals.append(signal)

        # Execute trades (max 3 per day)
        trade_results = []
        for signal in aligned_signals[:3]:
            result = self.simulate_trade_with_advanced_trailing(signal, daily_data)
            trade_results.append({
                'signal': signal,
                'result': result
            })

            # Update totals
            self.total_trades += 1
            if result['outcome'] == 'Win':
                self.winning_trades += 1
            self.total_pnl += result['pnl']

        # Calculate daily P&L
        daily_pnl = sum([trade['result']['pnl'] for trade in trade_results])

        return {
            'date': target_date,
            'gap_analysis': gap_analysis,
            'gap_continuation': gap_continuation,
            'gap_history_at_time': self.gap_history.copy(),  # Historical context
            'total_signals': len(eqh_eql_signals),
            'filtered_signals': len(filtered_signals),
            'aligned_signals': len(aligned_signals),
            'trades_taken': trade_results,
            'daily_pnl': daily_pnl,
            'daily_high': daily_data['high'].max(),
            'daily_low': daily_data['low'].min(),
            'daily_range': daily_data['high'].max() - daily_data['low'].min(),
            'opening_price': daily_data.iloc[0]['open'],
            'closing_price': daily_data.iloc[-1]['close']
        }

    def check_gap_alignment(self, signal, gap_analysis, gap_continuation):
        """Check if signal aligns with gap and continuation analysis"""
        if not gap_analysis:
            return False

        current_gap = gap_analysis['sentiment']
        signal_direction = signal['direction']

        # If character change detected, look for reversal
        if gap_continuation['character_change']:
            return (current_gap == 'Bullish' and signal_direction == 'Bearish') or \
                   (current_gap == 'Bearish' and signal_direction == 'Bullish')

        # If continuation expected, align with gap
        elif gap_continuation['continuation_expected']:
            return (current_gap == 'Bullish' and signal_direction == 'Bullish') or \
                   (current_gap == 'Bearish' and signal_direction == 'Bearish')

        # Default: align with current gap
        return (current_gap == 'Bullish' and signal_direction == 'Bullish') or \
               (current_gap == 'Bearish' and signal_direction == 'Bearish')

    def run_sequential_backtest(self):
        """Run complete sequential backtest"""
        print("\n🚀 STARTING SEQUENTIAL BACKTEST WITH ADVANCED RULES")
        print("="*80)
        print("⚠️  IMPORTANT: Each day processed as LIVE DATA - No future data used!")
        print(f"📅 Processing {len(self.all_trading_dates)} trading days sequentially...")

        for i, date in enumerate(self.all_trading_dates):
            daily_report = self.process_single_day(date)

            if daily_report:
                self.daily_reports.append(daily_report)

                # Progress update
                if (i + 1) % 10 == 0:
                    current_win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
                    print(f"📊 Day {i+1}/{len(self.all_trading_dates)} | "
                          f"Trades: {self.total_trades} | "
                          f"Win Rate: {current_win_rate:.1f}% | "
                          f"P&L: {self.total_pnl:+.1f}pts")

        print(f"\n✅ SEQUENTIAL BACKTEST COMPLETE!")
        print(f"📊 Final Results:")
        print(f"   Total Days: {len(self.daily_reports)}")
        print(f"   Total Trades: {self.total_trades}")
        print(f"   Win Rate: {(self.winning_trades/self.total_trades*100):.1f}%" if self.total_trades > 0 else "N/A")
        print(f"   Total P&L: {self.total_pnl:+.1f} points")
        print(f"   Avg Daily P&L: {(self.total_pnl/len(self.daily_reports)):.1f} points" if self.daily_reports else "N/A")

        return self.daily_reports

    def generate_detailed_report(self):
        """Generate detailed backtest report"""
        if not self.daily_reports:
            return "No data to report"

        # Calculate statistics
        total_days = len(self.daily_reports)
        profitable_days = sum(1 for day in self.daily_reports if day['daily_pnl'] > 0)
        loss_days = sum(1 for day in self.daily_reports if day['daily_pnl'] < 0)
        breakeven_days = total_days - profitable_days - loss_days

        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        avg_daily_pnl = self.total_pnl / total_days if total_days > 0 else 0

        # Count trailing stop usage
        trailing_trades = 0
        for day in self.daily_reports:
            for trade in day['trades_taken']:
                if trade['result'].get('trailing_used', False):
                    trailing_trades += 1

        # Generate report
        report = f"\n{'='*80}\n"
        report += f"🎯 SEQUENTIAL BACKTEST REPORT - ADVANCED RULES\n"
        report += f"{'='*80}\n"
        report += f"\n📊 PERFORMANCE SUMMARY:\n"
        report += f"   Analysis Period: {self.daily_reports[0]['date']} to {self.daily_reports[-1]['date']}\n"
        report += f"   Trading Days: {total_days}\n"
        report += f"   Total Trades: {self.total_trades}\n"
        report += f"   Winning Trades: {self.winning_trades}\n"
        report += f"   Win Rate: {win_rate:.1f}%\n"
        report += f"   Total P&L: {self.total_pnl:+.1f} points\n"
        report += f"   Average Daily P&L: {avg_daily_pnl:+.1f} points\n"
        report += f"\n📈 DAILY BREAKDOWN:\n"
        report += f"   Profitable Days: {profitable_days} ({profitable_days/total_days*100:.1f}%)\n"
        report += f"   Loss Days: {loss_days} ({loss_days/total_days*100:.1f}%)\n"
        report += f"   Breakeven Days: {breakeven_days} ({breakeven_days/total_days*100:.1f}%)\n"
        report += f"\n🚀 ADVANCED FEATURES PERFORMANCE:\n"
        report += f"   Trades Using Trailing Stops: {trailing_trades} ({trailing_trades/self.total_trades*100:.1f}%)\n"
        report += f"   Gap Continuation Analysis: Used on all {total_days} days\n"

        # Best and worst days
        if self.daily_reports:
            best_day = max(self.daily_reports, key=lambda x: x['daily_pnl'])
            worst_day = min(self.daily_reports, key=lambda x: x['daily_pnl'])
            report += f"\n🏆 BEST DAY: {best_day['date']} (+{best_day['daily_pnl']:.1f} points)\n"
            report += f"📉 WORST DAY: {worst_day['date']} ({worst_day['daily_pnl']:+.1f} points)\n"

        return report

def main():
    """Run sequential backtest"""
    try:
        print("🚀 SEQUENTIAL BACKTEST WITH ADVANCED RULES")
        print("📋 True Sequential Processing - No Future Data Leakage")
        print("="*80)

        # Initialize backtest engine
        engine = SequentialBacktestEngine('Nifty.csv')

        # Run sequential backtest
        daily_reports = engine.run_sequential_backtest()

        # Generate detailed report
        detailed_report = engine.generate_detailed_report()
        print(detailed_report)

        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"sequential_backtest_advanced_{timestamp}.txt"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(detailed_report)
            f.write("\n\n" + "="*80 + "\n")
            f.write("DETAILED DAILY REPORTS\n")
            f.write("="*80 + "\n")

            # Add daily details
            for day_report in daily_reports:
                f.write(f"\n📅 {day_report['date'].strftime('%A, %B %d, %Y')}\n")
                f.write("-" * 60 + "\n")
                f.write(f"Gap: {day_report['gap_analysis']['sentiment'] if day_report['gap_analysis'] else 'N/A'} | ")
                f.write(f"Continuation: {day_report['gap_continuation']['pattern']} | ")
                f.write(f"Trades: {len(day_report['trades_taken'])} | ")
                f.write(f"P&L: {day_report['daily_pnl']:+.1f}pts\n")

                for i, trade in enumerate(day_report['trades_taken'], 1):
                    signal = trade['signal']
                    result = trade['result']
                    f.write(f"  Trade {i}: {signal['type']} | {signal['entry_type']} | ")
                    f.write(f"₹{signal['price']:.2f} | {result['outcome']} | ")
                    f.write(f"{result['pnl']:+.1f}pts | {result['exit_reason']}\n")

        print(f"\n📄 Detailed report saved to: {report_filename}")
        print("✅ Sequential backtest complete!")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
