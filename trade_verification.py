#!/usr/bin/env python3
"""
Trade Verification Analysis
Verifies if the recommended trades from the previous analysis would have been profitable
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TradeVerifier:
    def __init__(self, data_file):
        """Initialize with updated data"""
        self.df = pd.read_csv(data_file, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        self.df = self.df.sort_values('datetime').reset_index(drop=True)
        
        # Original analysis cutoff date
        self.analysis_date = pd.to_datetime('2025-08-08 15:25:00')
        
        # Split data into analysis period and verification period
        self.analysis_data = self.df[self.df['datetime'] <= self.analysis_date].copy()
        self.verification_data = self.df[self.df['datetime'] > self.analysis_date].copy()
        
        print(f"📊 Analysis period: {self.analysis_data['datetime'].min()} to {self.analysis_data['datetime'].max()}")
        print(f"🔍 Verification period: {self.verification_data['datetime'].min()} to {self.verification_data['datetime'].max()}")
        print(f"📈 New data points: {len(self.verification_data):,}")
    
    def verify_sell_trade(self):
        """Verify the recommended SELL trade from the report"""
        print("\n" + "="*80)
        print("🔴 VERIFYING SELL TRADE RECOMMENDATION")
        print("="*80)
        
        # Trade parameters from the report
        recommended_entry = 24413.05
        stop_loss = 24470.00
        target_1 = 24300.00
        target_2 = 24250.00
        
        print(f"📋 Original Recommendation (Aug 8, 2025):")
        print(f"   Entry Price: ₹{recommended_entry:.2f}")
        print(f"   Stop Loss: ₹{stop_loss:.2f}")
        print(f"   Target 1: ₹{target_1:.2f}")
        print(f"   Target 2: ₹{target_2:.2f}")
        print(f"   Risk per share: ₹{stop_loss - recommended_entry:.2f}")
        print(f"   Reward per share (T1): ₹{recommended_entry - target_1:.2f}")
        print(f"   Risk-Reward Ratio: 1:{(recommended_entry - target_1)/(stop_loss - recommended_entry):.2f}")
        
        # Check if entry was triggered
        entry_triggered = False
        entry_time = None
        entry_price = None
        
        # Look for retest of the entry level (allowing some tolerance)
        tolerance = 5.0  # ₹5 tolerance
        
        for i, row in self.verification_data.iterrows():
            # Check if price reached entry level (for short, we need price to go up to entry)
            if row['high'] >= (recommended_entry - tolerance):
                entry_triggered = True
                entry_time = row['datetime']
                entry_price = recommended_entry  # Assume we got filled at recommended price
                break
        
        if not entry_triggered:
            print(f"\n❌ TRADE NOT TRIGGERED")
            print(f"   Price never reached entry level of ₹{recommended_entry:.2f}")
            print(f"   Highest price after signal: ₹{self.verification_data['high'].max():.2f}")
            return None
        
        print(f"\n✅ TRADE TRIGGERED")
        print(f"   Entry Time: {entry_time}")
        print(f"   Entry Price: ₹{entry_price:.2f}")
        
        # Now track the trade outcome
        trade_data = self.verification_data[self.verification_data['datetime'] >= entry_time].copy()
        
        stop_hit = False
        target_1_hit = False
        target_2_hit = False
        exit_time = None
        exit_price = None
        exit_reason = None
        
        for i, row in trade_data.iterrows():
            # Check stop loss (price goes against us - up for short)
            if row['high'] >= stop_loss:
                stop_hit = True
                exit_time = row['datetime']
                exit_price = stop_loss
                exit_reason = "Stop Loss Hit"
                break
            
            # Check target 1 (price goes in our favor - down for short)
            if row['low'] <= target_1:
                target_1_hit = True
                exit_time = row['datetime']
                exit_price = target_1
                exit_reason = "Target 1 Reached"
                
                # Check if target 2 was also hit in the same or later candles
                remaining_data = trade_data[trade_data['datetime'] >= entry_time]
                for j, row2 in remaining_data.iterrows():
                    if row2['low'] <= target_2:
                        target_2_hit = True
                        exit_price = target_2
                        exit_reason = "Target 2 Reached"
                        break
                break
        
        # If no stop or target hit, check current status
        if not stop_hit and not target_1_hit:
            current_price = self.verification_data.iloc[-1]['close']
            exit_time = self.verification_data.iloc[-1]['datetime']
            exit_price = current_price
            exit_reason = "Trade Still Open"
        
        # Calculate P&L
        pnl_per_share = entry_price - exit_price  # For short position
        pnl_percentage = (pnl_per_share / entry_price) * 100
        
        print(f"\n📊 TRADE OUTCOME:")
        print(f"   Exit Time: {exit_time}")
        print(f"   Exit Price: ₹{exit_price:.2f}")
        print(f"   Exit Reason: {exit_reason}")
        print(f"   P&L per share: ₹{pnl_per_share:.2f}")
        print(f"   P&L Percentage: {pnl_percentage:.2f}%")
        
        if pnl_per_share > 0:
            print(f"   🟢 PROFITABLE TRADE!")
        elif pnl_per_share < 0:
            print(f"   🔴 LOSING TRADE")
        else:
            print(f"   ⚪ BREAKEVEN TRADE")
        
        # Additional statistics
        if entry_triggered:
            max_favorable = trade_data['low'].min()  # Best price for short
            max_adverse = trade_data['high'].max()   # Worst price for short
            
            max_profit_potential = entry_price - max_favorable
            max_loss_potential = max_adverse - entry_price
            
            print(f"\n📈 TRADE STATISTICS:")
            print(f"   Maximum Favorable Move: ₹{max_profit_potential:.2f} ({(max_profit_potential/entry_price)*100:.2f}%)")
            print(f"   Maximum Adverse Move: ₹{max_loss_potential:.2f} ({(max_loss_potential/entry_price)*100:.2f}%)")
            print(f"   Best Possible Exit: ₹{max_favorable:.2f}")
            print(f"   Worst Moment: ₹{max_adverse:.2f}")
        
        return {
            'trade_type': 'SELL',
            'entry_triggered': entry_triggered,
            'entry_time': entry_time,
            'entry_price': entry_price,
            'exit_time': exit_time,
            'exit_price': exit_price,
            'exit_reason': exit_reason,
            'pnl_per_share': pnl_per_share,
            'pnl_percentage': pnl_percentage,
            'profitable': pnl_per_share > 0,
            'stop_hit': stop_hit,
            'target_1_hit': target_1_hit,
            'target_2_hit': target_2_hit
        }
    
    def analyze_market_movement(self):
        """Analyze overall market movement after the signal"""
        print("\n" + "="*80)
        print("📊 MARKET MOVEMENT ANALYSIS")
        print("="*80)
        
        signal_price = 24350.85  # Price at time of analysis
        current_price = self.verification_data.iloc[-1]['close']
        
        total_move = current_price - signal_price
        total_move_pct = (total_move / signal_price) * 100
        
        print(f"📈 Price at Analysis Time: ₹{signal_price:.2f}")
        print(f"📈 Current Price: ₹{current_price:.2f}")
        print(f"📊 Total Move: ₹{total_move:.2f} ({total_move_pct:.2f}%)")
        
        if total_move > 0:
            print(f"🟢 Market moved UP after our bearish signal")
        else:
            print(f"🔴 Market moved DOWN after our bearish signal")
        
        # Find highest and lowest points
        highest_price = self.verification_data['high'].max()
        lowest_price = self.verification_data['low'].min()
        
        highest_time = self.verification_data[self.verification_data['high'] == highest_price]['datetime'].iloc[0]
        lowest_time = self.verification_data[self.verification_data['low'] == lowest_price]['datetime'].iloc[0]
        
        print(f"\n📊 EXTREME POINTS:")
        print(f"   Highest Point: ₹{highest_price:.2f} on {highest_time}")
        print(f"   Lowest Point: ₹{lowest_price:.2f} on {lowest_time}")
        print(f"   Total Range: ₹{highest_price - lowest_price:.2f}")
        
        return {
            'signal_price': signal_price,
            'current_price': current_price,
            'total_move': total_move,
            'total_move_pct': total_move_pct,
            'highest_price': highest_price,
            'lowest_price': lowest_price,
            'highest_time': highest_time,
            'lowest_time': lowest_time
        }
    
    def generate_verification_report(self):
        """Generate comprehensive verification report"""
        print("🚀 TRADE VERIFICATION ANALYSIS")
        print("📋 Checking profitability of previous recommendations")
        print("-" * 60)
        
        # Verify the sell trade
        sell_result = self.verify_sell_trade()
        
        # Analyze market movement
        market_analysis = self.analyze_market_movement()
        
        # Summary
        print("\n" + "="*80)
        print("📋 VERIFICATION SUMMARY")
        print("="*80)
        
        if sell_result and sell_result['entry_triggered']:
            if sell_result['profitable']:
                print("✅ RECOMMENDED TRADE WAS PROFITABLE!")
                print(f"   Profit: ₹{sell_result['pnl_per_share']:.2f} per share ({sell_result['pnl_percentage']:.2f}%)")
            else:
                print("❌ RECOMMENDED TRADE WAS NOT PROFITABLE")
                print(f"   Loss: ₹{abs(sell_result['pnl_per_share']):.2f} per share ({abs(sell_result['pnl_percentage']):.2f}%)")
        else:
            print("⚪ TRADE WAS NOT TRIGGERED - NO ENTRY OPPORTUNITY")
        
        print(f"\n📊 SIGNAL ACCURACY:")
        if market_analysis['total_move'] < 0:
            print("✅ BEARISH SIGNAL WAS CORRECT - Market moved down")
        else:
            print("❌ BEARISH SIGNAL WAS INCORRECT - Market moved up")
        
        print(f"\n💡 LESSONS LEARNED:")
        if sell_result and sell_result['entry_triggered']:
            if sell_result['target_2_hit']:
                print("🎯 Both targets were achievable - excellent trade setup")
            elif sell_result['target_1_hit']:
                print("🎯 First target was achievable - good trade setup")
            elif sell_result['stop_hit']:
                print("🛑 Stop loss was hit - risk management worked")
            else:
                print("⏳ Trade is still developing")
        
        return sell_result, market_analysis

def main():
    try:
        verifier = TradeVerifier('Nifty.csv')
        sell_result, market_analysis = verifier.generate_verification_report()
        
        print("\n✅ Verification Complete!")
        print("💡 This analysis helps improve future trading decisions.")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
