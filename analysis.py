import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class PriceActionAnalyzer:
    def __init__(self, data_file):
        """Initialize the analyzer with Nifty 50 data"""
        # Load data with proper dtype handling
        self.df = pd.read_csv(data_file, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        self.df = self.df.sort_values('datetime').reset_index(drop=True)

        # For performance, work with recent data (last 30 days)
        recent_date = self.df['datetime'].max() - pd.Timedelta(days=30)
        self.df = self.df[self.df['datetime'] >= recent_date].reset_index(drop=True)
        
        # Parameters from Pine Script
        self.internal_lookback = 5  # internal_r_lookback
        self.swing_lookback = 50   # swing_r_lookback
        
        # Initialize arrays for market structure
        self.signals = []
        self.order_blocks = []
        self.fvgs = []
        
    def calculate_pivots(self, lookback):
        """Calculate pivot highs and lows"""
        highs = []
        lows = []
        
        for i in range(lookback, len(self.df) - lookback):
            # Check for pivot high
            is_pivot_high = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and self.df.iloc[j]['high'] >= self.df.iloc[i]['high']:
                    is_pivot_high = False
                    break
            
            # Check for pivot low
            is_pivot_low = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and self.df.iloc[j]['low'] <= self.df.iloc[i]['low']:
                    is_pivot_low = False
                    break
            
            highs.append(self.df.iloc[i]['high'] if is_pivot_high else np.nan)
            lows.append(self.df.iloc[i]['low'] if is_pivot_low else np.nan)
        
        # Pad with NaN for the edges
        highs = [np.nan] * lookback + highs + [np.nan] * lookback
        lows = [np.nan] * lookback + lows + [np.nan] * lookback
        
        return highs, lows
    
    def detect_market_structure(self):
        """Detect BOS and CHoCH patterns"""
        # Calculate internal and swing pivots
        i_highs, i_lows = self.calculate_pivots(self.internal_lookback)
        s_highs, s_lows = self.calculate_pivots(self.swing_lookback)
        
        self.df['i_high'] = i_highs
        self.df['i_low'] = i_lows
        self.df['s_high'] = s_highs
        self.df['s_low'] = s_lows
        
        # Track trend direction
        internal_trend = 0
        swing_trend = 0
        
        # Arrays to store pivot points
        internal_highs = []
        internal_lows = []
        swing_highs = []
        swing_lows = []
        
        for i in range(len(self.df)):
            row = self.df.iloc[i]
            
            # Store pivot points
            if not pd.isna(row['i_high']):
                internal_highs.append({'index': i, 'price': row['i_high'], 'time': row['datetime']})
            if not pd.isna(row['i_low']):
                internal_lows.append({'index': i, 'price': row['i_low'], 'time': row['datetime']})
            if not pd.isna(row['s_high']):
                swing_highs.append({'index': i, 'price': row['s_high'], 'time': row['datetime']})
            if not pd.isna(row['s_low']):
                swing_lows.append({'index': i, 'price': row['s_low'], 'time': row['datetime']})
            
            # Check for internal structure breaks
            if len(internal_highs) > 0 and row['close'] > internal_highs[-1]['price']:
                if internal_trend <= 0:  # Was bearish or neutral
                    signal_type = "CHoCH" if internal_trend < 0 else "BOS"
                    self.signals.append({
                        'index': i,
                        'time': row['datetime'],
                        'price': row['close'],
                        'type': f"Internal {signal_type} Bullish",
                        'timeframe': 'Internal',
                        'direction': 'Bullish',
                        'entry_type': 'BUY'
                    })
                internal_trend = 1
                
            elif len(internal_lows) > 0 and row['close'] < internal_lows[-1]['price']:
                if internal_trend >= 0:  # Was bullish or neutral
                    signal_type = "CHoCH" if internal_trend > 0 else "BOS"
                    self.signals.append({
                        'index': i,
                        'time': row['datetime'],
                        'price': row['close'],
                        'type': f"Internal {signal_type} Bearish",
                        'timeframe': 'Internal',
                        'direction': 'Bearish',
                        'entry_type': 'SELL'
                    })
                internal_trend = -1
            
            # Check for swing structure breaks
            if len(swing_highs) > 0 and row['close'] > swing_highs[-1]['price']:
                if swing_trend <= 0:
                    signal_type = "CHoCH" if swing_trend < 0 else "BOS"
                    self.signals.append({
                        'index': i,
                        'time': row['datetime'],
                        'price': row['close'],
                        'type': f"Swing {signal_type} Bullish",
                        'timeframe': 'Swing',
                        'direction': 'Bullish',
                        'entry_type': 'BUY'
                    })
                swing_trend = 1
                
            elif len(swing_lows) > 0 and row['close'] < swing_lows[-1]['price']:
                if swing_trend >= 0:
                    signal_type = "CHoCH" if swing_trend > 0 else "BOS"
                    self.signals.append({
                        'index': i,
                        'time': row['datetime'],
                        'price': row['close'],
                        'type': f"Swing {signal_type} Bearish",
                        'timeframe': 'Swing',
                        'direction': 'Bearish',
                        'entry_type': 'SELL'
                    })
                swing_trend = -1
    
    def detect_order_blocks(self):
        """Detect order blocks based on volume and price action"""
        for i in range(3, len(self.df) - 1):
            current = self.df.iloc[i]
            prev1 = self.df.iloc[i-1]
            prev2 = self.df.iloc[i-2]
            prev3 = self.df.iloc[i-3]
            next1 = self.df.iloc[i+1]
            
            # Bullish Order Block: Look for strong buying after a down move
            if (prev3['close'] > prev2['close'] > prev1['close'] and  # Downtrend
                current['close'] > current['open'] and  # Bullish candle
                current['close'] > prev1['high'] and  # Break above previous high
                next1['low'] > current['low']):  # Next candle respects the low
                
                self.order_blocks.append({
                    'index': i,
                    'time': current['datetime'],
                    'type': 'Bullish OB',
                    'top': current['high'],
                    'bottom': current['low'],
                    'direction': 'Bullish',
                    'entry_price': current['low'],
                    'target': current['high'] + (current['high'] - current['low'])
                })
            
            # Bearish Order Block: Look for strong selling after an up move
            elif (prev3['close'] < prev2['close'] < prev1['close'] and  # Uptrend
                  current['close'] < current['open'] and  # Bearish candle
                  current['close'] < prev1['low'] and  # Break below previous low
                  next1['high'] < current['high']):  # Next candle respects the high
                
                self.order_blocks.append({
                    'index': i,
                    'time': current['datetime'],
                    'type': 'Bearish OB',
                    'top': current['high'],
                    'bottom': current['low'],
                    'direction': 'Bearish',
                    'entry_price': current['high'],
                    'target': current['low'] - (current['high'] - current['low'])
                })
    
    def detect_fair_value_gaps(self):
        """Detect Fair Value Gaps (FVG)"""
        for i in range(2, len(self.df)):
            candle1 = self.df.iloc[i-2]  # First candle
            candle2 = self.df.iloc[i-1]  # Middle candle
            candle3 = self.df.iloc[i]    # Third candle
            
            # Bullish FVG: Gap between candle1 high and candle3 low
            if (candle2['close'] > candle2['open'] and  # Middle candle is bullish
                candle3['low'] > candle1['high']):      # Gap exists
                
                self.fvgs.append({
                    'index': i,
                    'time': candle3['datetime'],
                    'type': 'Bullish FVG',
                    'top': candle3['low'],
                    'bottom': candle1['high'],
                    'direction': 'Bullish'
                })
            
            # Bearish FVG: Gap between candle1 low and candle3 high
            elif (candle2['close'] < candle2['open'] and  # Middle candle is bearish
                  candle3['high'] < candle1['low']):      # Gap exists
                
                self.fvgs.append({
                    'index': i,
                    'time': candle3['datetime'],
                    'type': 'Bearish FVG',
                    'top': candle1['low'],
                    'bottom': candle3['high'],
                    'direction': 'Bearish'
                })
    
    def analyze(self):
        """Run complete analysis"""
        print("🔍 Analyzing Nifty 50 data according to Pine Script strategy...")
        print(f"📊 Data range: {self.df['datetime'].min()} to {self.df['datetime'].max()}")
        print(f"📈 Total candles: {len(self.df):,}")
        
        # Run all detection methods
        self.detect_market_structure()
        self.detect_order_blocks()
        self.detect_fair_value_gaps()
        
        print(f"\n✅ Analysis complete!")
        print(f"🎯 Market Structure Signals: {len(self.signals)}")
        print(f"📦 Order Blocks: {len(self.order_blocks)}")
        print(f"⚡ Fair Value Gaps: {len(self.fvgs)}")
        
        return self.signals, self.order_blocks, self.fvgs

    def get_trading_recommendations(self, recent_days=30):
        """Generate trading recommendations based on recent signals"""
        # Filter recent data
        recent_date = self.df['datetime'].max() - pd.Timedelta(days=recent_days)
        recent_signals = [s for s in self.signals if s['time'] >= recent_date]
        recent_obs = [ob for ob in self.order_blocks if ob['time'] >= recent_date]
        recent_fvgs = [fvg for fvg in self.fvgs if fvg['time'] >= recent_date]

        recommendations = []

        # Analyze recent market structure signals
        if recent_signals:
            latest_signal = recent_signals[-1]

            if latest_signal['direction'] == 'Bullish':
                recommendations.append({
                    'type': 'BUY',
                    'reason': f"Latest {latest_signal['type']} signal",
                    'entry_price': latest_signal['price'],
                    'time': latest_signal['time'],
                    'confidence': 'High' if 'Swing' in latest_signal['type'] else 'Medium',
                    'strategy': 'Market Structure Break'
                })
            else:
                recommendations.append({
                    'type': 'SELL',
                    'reason': f"Latest {latest_signal['type']} signal",
                    'entry_price': latest_signal['price'],
                    'time': latest_signal['time'],
                    'confidence': 'High' if 'Swing' in latest_signal['type'] else 'Medium',
                    'strategy': 'Market Structure Break'
                })

        # Analyze order blocks for entry opportunities
        for ob in recent_obs[-5:]:  # Last 5 order blocks
            current_price = self.df.iloc[-1]['close']

            if ob['direction'] == 'Bullish' and current_price <= ob['top'] and current_price >= ob['bottom']:
                recommendations.append({
                    'type': 'BUY',
                    'reason': f"Price in Bullish Order Block zone",
                    'entry_price': ob['entry_price'],
                    'target': ob['target'],
                    'time': ob['time'],
                    'confidence': 'High',
                    'strategy': 'Order Block Retest'
                })
            elif ob['direction'] == 'Bearish' and current_price <= ob['top'] and current_price >= ob['bottom']:
                recommendations.append({
                    'type': 'SELL',
                    'reason': f"Price in Bearish Order Block zone",
                    'entry_price': ob['entry_price'],
                    'target': ob['target'],
                    'time': ob['time'],
                    'confidence': 'High',
                    'strategy': 'Order Block Retest'
                })

        # Analyze FVGs for potential reversals
        for fvg in recent_fvgs[-3:]:  # Last 3 FVGs
            current_price = self.df.iloc[-1]['close']

            if fvg['direction'] == 'Bullish' and current_price <= fvg['top'] and current_price >= fvg['bottom']:
                recommendations.append({
                    'type': 'BUY',
                    'reason': f"Price in Bullish Fair Value Gap",
                    'entry_price': fvg['bottom'],
                    'target': fvg['top'],
                    'time': fvg['time'],
                    'confidence': 'Medium',
                    'strategy': 'Fair Value Gap Fill'
                })
            elif fvg['direction'] == 'Bearish' and current_price <= fvg['top'] and current_price >= fvg['bottom']:
                recommendations.append({
                    'type': 'SELL',
                    'reason': f"Price in Bearish Fair Value Gap",
                    'entry_price': fvg['top'],
                    'target': fvg['bottom'],
                    'time': fvg['time'],
                    'confidence': 'Medium',
                    'strategy': 'Fair Value Gap Fill'
                })

        return recommendations

    def plot_analysis(self, days_back=10, save_plot=True):
        """Create visualization of the analysis"""
        # Filter recent data for plotting
        recent_date = self.df['datetime'].max() - pd.Timedelta(days=days_back)
        plot_df = self.df[self.df['datetime'] >= recent_date].copy()

        if len(plot_df) == 0:
            print("No data available for the specified time range")
            return

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[3, 1])

        # Plot candlestick chart
        for i in range(len(plot_df)):
            row = plot_df.iloc[i]
            color = 'green' if row['close'] > row['open'] else 'red'

            # Candlestick body
            ax1.plot([i, i], [row['low'], row['high']], color='black', linewidth=0.5)
            ax1.plot([i, i], [row['open'], row['close']], color=color, linewidth=3)

        # Plot signals
        recent_signals = [s for s in self.signals if s['time'] >= recent_date]
        for signal in recent_signals:
            signal_idx = plot_df[plot_df['datetime'] == signal['time']].index
            if len(signal_idx) > 0:
                idx = signal_idx[0] - plot_df.index[0]
                color = 'green' if signal['direction'] == 'Bullish' else 'red'
                marker = '^' if signal['direction'] == 'Bullish' else 'v'
                ax1.scatter(idx, signal['price'], color=color, marker=marker, s=100, zorder=5)
                ax1.annotate(signal['type'], (idx, signal['price']),
                           xytext=(5, 10), textcoords='offset points', fontsize=8)

        # Plot order blocks
        recent_obs = [ob for ob in self.order_blocks if ob['time'] >= recent_date]
        for ob in recent_obs:
            ob_idx = plot_df[plot_df['datetime'] == ob['time']].index
            if len(ob_idx) > 0:
                idx = ob_idx[0] - plot_df.index[0]
                color = 'lightgreen' if ob['direction'] == 'Bullish' else 'lightcoral'
                ax1.axhspan(ob['bottom'], ob['top'], xmin=idx/len(plot_df), xmax=1,
                           alpha=0.3, color=color, label=f"{ob['type']}")

        ax1.set_title(f'Nifty 50 - Price Action Analysis (Last {days_back} days)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Price', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Plot volume
        ax2.bar(range(len(plot_df)), plot_df['close'], alpha=0.7, color='blue')
        ax2.set_title('Volume', fontsize=12)
        ax2.set_xlabel('Time', fontsize=12)
        ax2.set_ylabel('Volume', fontsize=12)

        # Format x-axis
        ax1.set_xticks(range(0, len(plot_df), max(1, len(plot_df)//10)))
        ax1.set_xticklabels([plot_df.iloc[i]['datetime'].strftime('%m/%d %H:%M')
                            for i in range(0, len(plot_df), max(1, len(plot_df)//10))],
                           rotation=45)

        ax2.set_xticks(range(0, len(plot_df), max(1, len(plot_df)//10)))
        ax2.set_xticklabels([plot_df.iloc[i]['datetime'].strftime('%m/%d %H:%M')
                            for i in range(0, len(plot_df), max(1, len(plot_df)//10))],
                           rotation=45)

        plt.tight_layout()

        if save_plot:
            plt.savefig('nifty_analysis.png', dpi=300, bbox_inches='tight')
            print("📊 Chart saved as 'nifty_analysis.png'")

        plt.show()

    def print_summary(self):
        """Print detailed analysis summary"""
        print("\n" + "="*80)
        print("🎯 NIFTY 50 TRADING ANALYSIS SUMMARY")
        print("="*80)

        # Recent signals summary
        recent_signals = [s for s in self.signals if s['time'] >= self.df['datetime'].max() - pd.Timedelta(days=7)]

        print(f"\n📈 RECENT MARKET STRUCTURE SIGNALS (Last 7 days): {len(recent_signals)}")
        print("-" * 60)

        for signal in recent_signals[-10:]:  # Last 10 signals
            print(f"🕐 {signal['time'].strftime('%Y-%m-%d %H:%M')} | "
                  f"{signal['type']} | Price: ₹{signal['price']:.2f} | "
                  f"Action: {signal['entry_type']}")

        # Order blocks summary
        recent_obs = [ob for ob in self.order_blocks if ob['time'] >= self.df['datetime'].max() - pd.Timedelta(days=7)]

        print(f"\n📦 RECENT ORDER BLOCKS (Last 7 days): {len(recent_obs)}")
        print("-" * 60)

        for ob in recent_obs[-5:]:  # Last 5 order blocks
            print(f"🕐 {ob['time'].strftime('%Y-%m-%d %H:%M')} | "
                  f"{ob['type']} | Range: ₹{ob['bottom']:.2f} - ₹{ob['top']:.2f} | "
                  f"Entry: ₹{ob['entry_price']:.2f}")

        # Trading recommendations
        recommendations = self.get_trading_recommendations()

        print(f"\n🎯 CURRENT TRADING RECOMMENDATIONS: {len(recommendations)}")
        print("-" * 60)

        for i, rec in enumerate(recommendations[-5:], 1):  # Last 5 recommendations
            print(f"{i}. {rec['type']} | {rec['strategy']} | "
                  f"Entry: ₹{rec['entry_price']:.2f} | "
                  f"Confidence: {rec['confidence']} | "
                  f"Reason: {rec['reason']}")

        # Current market status
        current_price = self.df.iloc[-1]['close']
        latest_signal = self.signals[-1] if self.signals else None

        print(f"\n📊 CURRENT MARKET STATUS")
        print("-" * 60)
        print(f"💰 Current Price: ₹{current_price:.2f}")
        print(f"📅 Last Update: {self.df.iloc[-1]['datetime'].strftime('%Y-%m-%d %H:%M')}")

        if latest_signal:
            print(f"🎯 Latest Signal: {latest_signal['type']} at ₹{latest_signal['price']:.2f}")
            print(f"📈 Market Bias: {latest_signal['direction']}")

        print("\n" + "="*80)
