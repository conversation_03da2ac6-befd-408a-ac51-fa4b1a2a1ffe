#!/usr/bin/env python3
"""
Custom Analysis Framework
Allows integration of user-specific trading rules and instructions
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

class CustomAnalysisFramework:
    def __init__(self, data_file, custom_rules=None):
        """Initialize with data and custom rules"""
        self.df = pd.read_csv(data_file, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        self.df = self.df.sort_values('datetime').reset_index(drop=True)
        
        # Default rules (can be overridden by custom_rules)
        self.rules = {
            'signal_filters': {
                'min_volume_multiplier': 1.2,  # Minimum volume vs average
                'avoid_first_hour': True,      # Avoid first hour of trading
                'avoid_last_hour': True,       # Avoid last hour of trading
                'require_swing_confirmation': False,  # Require swing-level confirmation
                'min_candles_since_major_level': 5,   # Min distance from S/R levels
            },
            'entry_conditions': {
                'require_retest': True,        # Wait for retest of broken level
                'max_retest_distance': 10,     # Max distance for valid retest (points)
                'require_rejection_candle': True,  # Need rejection at entry level
                'min_risk_reward': 1.5,       # Minimum risk-reward ratio
            },
            'risk_management': {
                'max_risk_per_trade': 0.02,   # 2% max risk per trade
                'position_size_by_confidence': True,  # Adjust size by signal confidence
                'use_trailing_stops': True,   # Use trailing stops for winners
                'partial_profit_at_1R': True, # Take partial profit at 1:1 RR
            },
            'market_context': {
                'check_higher_timeframes': True,  # Check 15m, 1H, 4H trends
                'avoid_news_times': True,     # Avoid major news release times
                'consider_market_session': True,  # Consider pre-market, regular, post-market
                'check_volatility': True,     # Check if volatility is normal
            },
            'custom_filters': {
                # Add your specific filters here
                'nifty_specific_rules': True,
                'sector_rotation_check': False,
                'fii_dii_flow_check': False,
            }
        }
        
        # Override with custom rules if provided
        if custom_rules:
            self.update_rules(custom_rules)
        
        # Initialize analysis components
        self.signals = []
        self.filtered_signals = []
        self.rejected_signals = []
        
    def update_rules(self, custom_rules):
        """Update rules with custom instructions"""
        for category, rules in custom_rules.items():
            if category in self.rules:
                self.rules[category].update(rules)
            else:
                self.rules[category] = rules
    
    def get_market_session(self, dt):
        """Determine market session"""
        time_obj = dt.time()
        
        if time(9, 15) <= time_obj <= time(10, 15):
            return "opening"
        elif time(10, 15) <= time_obj <= time(14, 30):
            return "regular"
        elif time(14, 30) <= time_obj <= time(15, 30):
            return "closing"
        else:
            return "after_hours"
    
    def calculate_volume_ratio(self, index, lookback=20):
        """Calculate volume ratio vs average"""
        if index < lookback:
            return 1.0
        
        current_vol = self.df.iloc[index]['close']  # Using close as proxy for volume
        avg_vol = self.df.iloc[index-lookback:index]['close'].mean()
        
        return current_vol / avg_vol if avg_vol > 0 else 1.0
    
    def check_distance_from_major_levels(self, price, lookback=100):
        """Check distance from recent major highs/lows"""
        if len(self.df) < lookback:
            return True
        
        recent_data = self.df.tail(lookback)
        recent_high = recent_data['high'].max()
        recent_low = recent_data['low'].min()
        
        # Check if price is too close to major levels
        distance_from_high = abs(price - recent_high)
        distance_from_low = abs(price - recent_low)
        
        min_distance = self.rules['signal_filters']['min_candles_since_major_level'] * 5  # 5 points per candle
        
        return distance_from_high > min_distance and distance_from_low > min_distance
    
    def apply_signal_filters(self, signal):
        """Apply custom signal filters"""
        filters = self.rules['signal_filters']
        rejection_reasons = []
        
        # Time-based filters
        session = self.get_market_session(signal['time'])
        
        if filters['avoid_first_hour'] and session == "opening":
            rejection_reasons.append("First hour trading avoided")
        
        if filters['avoid_last_hour'] and session == "closing":
            rejection_reasons.append("Last hour trading avoided")
        
        # Volume filter
        if 'index' in signal:
            vol_ratio = self.calculate_volume_ratio(signal['index'])
            if vol_ratio < filters['min_volume_multiplier']:
                rejection_reasons.append(f"Low volume (ratio: {vol_ratio:.2f})")
        
        # Swing confirmation filter
        if filters['require_swing_confirmation'] and 'Internal' in signal['type']:
            rejection_reasons.append("Swing confirmation required for Internal signals")
        
        # Distance from major levels
        if not self.check_distance_from_major_levels(signal['price']):
            rejection_reasons.append("Too close to major support/resistance level")
        
        return len(rejection_reasons) == 0, rejection_reasons
    
    def apply_entry_conditions(self, signal):
        """Apply custom entry conditions"""
        conditions = self.rules['entry_conditions']
        rejection_reasons = []
        
        # Add your custom entry logic here
        # This is where you can add specific conditions like:
        # - Retest requirements
        # - Confirmation candle patterns
        # - Risk-reward calculations
        
        return len(rejection_reasons) == 0, rejection_reasons
    
    def calculate_position_size(self, signal, account_size=100000):
        """Calculate position size based on custom rules"""
        risk_mgmt = self.rules['risk_management']
        
        # Base risk amount
        risk_amount = account_size * risk_mgmt['max_risk_per_trade']
        
        # Adjust by confidence level
        if risk_mgmt['position_size_by_confidence']:
            confidence_multiplier = {
                'High': 1.0,
                'Medium': 0.7,
                'Low': 0.5
            }
            
            confidence = signal.get('confidence', 'Medium')
            risk_amount *= confidence_multiplier.get(confidence, 0.7)
        
        # Calculate position size based on stop distance
        if 'stop_distance' in signal:
            position_size = risk_amount / signal['stop_distance']
        else:
            position_size = risk_amount / 50  # Default 50 point stop
        
        return int(position_size)
    
    def analyze_with_custom_rules(self, base_signals):
        """Analyze signals with custom rules applied"""
        print("🔧 Applying Custom Analysis Rules...")
        print(f"📊 Base signals to filter: {len(base_signals)}")
        
        for signal in base_signals:
            # Apply signal filters
            passed_filters, filter_reasons = self.apply_signal_filters(signal)
            
            if not passed_filters:
                self.rejected_signals.append({
                    **signal,
                    'rejection_reasons': filter_reasons,
                    'rejection_stage': 'signal_filters'
                })
                continue
            
            # Apply entry conditions
            passed_entry, entry_reasons = self.apply_entry_conditions(signal)
            
            if not passed_entry:
                self.rejected_signals.append({
                    **signal,
                    'rejection_reasons': entry_reasons,
                    'rejection_stage': 'entry_conditions'
                })
                continue
            
            # Calculate position size
            position_size = self.calculate_position_size(signal)
            
            # Add to filtered signals
            filtered_signal = {
                **signal,
                'position_size': position_size,
                'passed_all_filters': True
            }
            
            self.filtered_signals.append(filtered_signal)
        
        print(f"✅ Signals passed filters: {len(self.filtered_signals)}")
        print(f"❌ Signals rejected: {len(self.rejected_signals)}")
        
        return self.filtered_signals, self.rejected_signals
    
    def print_custom_analysis_report(self):
        """Print detailed report with custom analysis"""
        print("\n" + "="*80)
        print("🔧 CUSTOM ANALYSIS REPORT")
        print("="*80)
        
        print(f"\n📋 ACTIVE CUSTOM RULES:")
        for category, rules in self.rules.items():
            print(f"\n{category.upper().replace('_', ' ')}:")
            for rule, value in rules.items():
                print(f"  • {rule}: {value}")
        
        if self.filtered_signals:
            print(f"\n✅ QUALIFIED SIGNALS ({len(self.filtered_signals)}):")
            print("-" * 60)
            for i, signal in enumerate(self.filtered_signals, 1):
                print(f"{i}. {signal['type']} | ₹{signal['price']:.2f} | "
                      f"Size: {signal['position_size']} | "
                      f"{signal['time'].strftime('%m/%d %H:%M')}")
        
        if self.rejected_signals:
            print(f"\n❌ REJECTED SIGNALS ({len(self.rejected_signals)}):")
            print("-" * 60)
            for i, signal in enumerate(self.rejected_signals[-5:], 1):  # Show last 5
                reasons = ", ".join(signal['rejection_reasons'])
                print(f"{i}. {signal['type']} | ₹{signal['price']:.2f} | "
                      f"Rejected: {reasons}")
        
        print("\n" + "="*80)

# Example of how to use custom rules
def get_sample_custom_rules():
    """Sample custom rules - modify these based on your preferences"""
    return {
        'signal_filters': {
            'min_volume_multiplier': 1.5,  # Higher volume requirement
            'avoid_first_hour': True,      # Your preference
            'require_swing_confirmation': True,  # Only swing signals
        },
        'entry_conditions': {
            'require_retest': True,        # Always wait for retest
            'min_risk_reward': 2.0,        # Higher RR requirement
        },
        'risk_management': {
            'max_risk_per_trade': 0.015,   # 1.5% max risk (more conservative)
        },
        'custom_filters': {
            'your_special_rule': True,     # Add your own rules here
        }
    }

if __name__ == "__main__":
    # Example usage
    custom_rules = get_sample_custom_rules()
    analyzer = CustomAnalysisFramework('Nifty.csv', custom_rules)
    
    # You would integrate this with your main analysis
    print("🔧 Custom Analysis Framework Ready!")
    print("📝 Modify the custom_rules to match your trading preferences")
