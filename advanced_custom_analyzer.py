#!/usr/bin/env python3
"""
Advanced Custom Analyzer with New Rules
Implements your latest advanced trading rules:
1. Gap continuation analysis
2. Advanced trailing stops
3. Character change detection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from your_custom_analyzer import YourCustomAnalyzer
from my_custom_rules import analyze_gap_continuation_pattern, my_custom_exit_logic

class AdvancedCustomAnalyzer(YourCustomAnalyzer):
    def __init__(self, data_file):
        """Initialize with advanced rules"""
        super().__init__(data_file)
        
        # Track gap history for continuation analysis
        self.gap_history = []
        self.daily_gap_sentiments = {}
        
        # Track active trades for trailing stops
        self.active_trades = []
        
        print("🚀 Advanced Custom Analyzer Initialized")
        print("✅ Gap Continuation Analysis: ENABLED")
        print("✅ Advanced Trailing Stops: ENABLED")
        print("✅ Character Change Detection: ENABLED")
    
    def build_gap_history(self):
        """Build historical gap sentiment data"""
        print("📊 Building gap history for continuation analysis...")
        
        # Get unique trading dates
        trading_dates = sorted(self.df['datetime'].dt.date.unique())
        
        for date in trading_dates:
            daily_data = self.get_daily_data(date)
            if daily_data is not None and len(daily_data) > 0:
                gap_analysis = self.analyze_gap_opening(daily_data)
                if gap_analysis:
                    self.daily_gap_sentiments[date] = gap_analysis['sentiment']
                    self.gap_history.append(gap_analysis['sentiment'])
        
        print(f"📈 Gap history built: {len(self.gap_history)} days")
        print(f"   Bullish gaps: {self.gap_history.count('Bullish')}")
        print(f"   Bearish gaps: {self.gap_history.count('Bearish')}")
    
    def get_recent_gap_pattern(self, current_date, lookback_days=5):
        """Get recent gap pattern for continuation analysis"""
        recent_gaps = []
        
        # Get last N days of gap sentiments
        for i in range(1, lookback_days + 1):
            check_date = current_date - timedelta(days=i)
            if check_date in self.daily_gap_sentiments:
                recent_gaps.append(self.daily_gap_sentiments[check_date])
        
        return recent_gaps[::-1]  # Reverse to get chronological order
    
    def analyze_single_day_advanced(self, target_date):
        """Enhanced daily analysis with new rules"""
        daily_data = self.get_daily_data(target_date)
        
        if daily_data is None or len(daily_data) == 0:
            return None
        
        # Standard gap analysis
        gap_analysis = self.analyze_gap_opening(daily_data)
        current_gap = gap_analysis['sentiment'] if gap_analysis else None
        
        # NEW: Gap continuation analysis
        recent_gaps = self.get_recent_gap_pattern(target_date)
        gap_continuation = analyze_gap_continuation_pattern(recent_gaps, current_gap)
        
        # Detect patterns with gap continuation context
        eqh_eql_signals = self.detect_eqh_eql_daily(daily_data)
        ms_signals = self.detect_market_structure_daily(daily_data)
        
        # Combine all signals
        all_signals = eqh_eql_signals + ms_signals
        
        # Apply timing filters
        filtered_signals = self.apply_your_timing_filters(all_signals)
        
        # NEW: Enhanced gap alignment with continuation analysis
        aligned_signals = []
        for signal in filtered_signals:
            gap_aligned = self.check_advanced_gap_alignment(signal, gap_analysis, gap_continuation)
            if gap_aligned:
                signal['gap_continuation'] = gap_continuation
                signal['advanced_gap_aligned'] = True
                aligned_signals.append(signal)
        
        # Simulate trades with advanced trailing
        trade_results = []
        for signal in aligned_signals[:3]:  # Max 3 trades per day
            result = self.simulate_advanced_trade(signal, daily_data)
            trade_results.append({
                'signal': signal,
                'result': result
            })
        
        # Calculate daily P&L
        daily_pnl = sum([trade['result']['pnl'] for trade in trade_results])
        
        return {
            'date': target_date,
            'gap_analysis': gap_analysis,
            'gap_continuation': gap_continuation,
            'recent_gap_pattern': recent_gaps,
            'total_signals': len(all_signals),
            'filtered_signals': len(filtered_signals),
            'aligned_signals': len(aligned_signals),
            'trades_taken': trade_results,
            'daily_pnl': daily_pnl,
            'daily_high': daily_data['high'].max(),
            'daily_low': daily_data['low'].min(),
            'daily_range': daily_data['high'].max() - daily_data['low'].min(),
            'opening_price': daily_data.iloc[0]['open'],
            'closing_price': daily_data.iloc[-1]['close']
        }
    
    def check_advanced_gap_alignment(self, signal, gap_analysis, gap_continuation):
        """Enhanced gap alignment check with continuation analysis"""
        if not gap_analysis:
            return False
        
        current_gap = gap_analysis['sentiment']
        signal_direction = signal['direction']
        
        # Basic alignment check
        basic_alignment = (current_gap == 'Bullish' and signal_direction == 'Bullish') or \
                         (current_gap == 'Bearish' and signal_direction == 'Bearish')
        
        # Enhanced alignment with continuation analysis
        if gap_continuation['character_change']:
            # Character change detected - look for reversal trades
            reversal_alignment = (current_gap == 'Bullish' and signal_direction == 'Bearish') or \
                                (current_gap == 'Bearish' and signal_direction == 'Bullish')
            return reversal_alignment
        
        elif gap_continuation['continuation_expected']:
            # Continuation expected - stick with gap direction
            return basic_alignment
        
        else:
            # No clear pattern - use basic alignment
            return basic_alignment
    
    def simulate_advanced_trade(self, signal, daily_data):
        """Simulate trade with advanced trailing stops"""
        entry_time = signal['time']
        entry_price = signal['price']
        direction = signal['direction']
        risk_points = signal['risk_points']
        reward_points = signal['reward_points']
        
        # Calculate initial stop loss and target
        if direction == 'Bullish':
            initial_stop = entry_price - risk_points
            initial_target = entry_price + reward_points
        else:
            initial_stop = entry_price + risk_points
            initial_target = entry_price - reward_points
        
        # Find data after entry time
        future_data = daily_data[daily_data['datetime'] > entry_time]
        
        if len(future_data) == 0:
            return {
                'outcome': 'No Data',
                'exit_price': entry_price,
                'exit_time': entry_time,
                'pnl': 0,
                'exit_reason': 'End of day',
                'max_profit': 0,
                'trailing_activated': False
            }
        
        # Track trade progress with advanced trailing
        current_stop = initial_stop
        max_profit = 0
        trailing_activated = False
        target_hit = False
        
        for _, row in future_data.iterrows():
            current_price = row['close']
            current_time = row['datetime']
            
            # Calculate current P&L
            if direction == 'Bullish':
                current_pnl = current_price - entry_price
            else:
                current_pnl = entry_price - current_price
            
            max_profit = max(max_profit, current_pnl)
            
            # Check if initial target is hit
            if not target_hit and current_pnl >= reward_points:
                target_hit = True
                trailing_activated = True
                # Move stop to breakeven
                current_stop = entry_price
                
                # Apply advanced trailing logic
                exit_decision = my_custom_exit_logic(
                    {**signal, 'entry_price': entry_price}, 
                    current_price, 
                    daily_data
                )
                
                if exit_decision['action'] == 'update_stop':
                    current_stop = exit_decision['new_stop_loss']
            
            # Check stop loss
            if direction == 'Bullish':
                if row['low'] <= current_stop:
                    return {
                        'outcome': 'Win' if current_pnl > 0 else 'Loss',
                        'exit_price': current_stop,
                        'exit_time': current_time,
                        'pnl': current_stop - entry_price if direction == 'Bullish' else entry_price - current_stop,
                        'exit_reason': 'Trailing Stop Hit' if trailing_activated else 'Stop Loss Hit',
                        'max_profit': max_profit,
                        'trailing_activated': trailing_activated
                    }
            else:
                if row['high'] >= current_stop:
                    return {
                        'outcome': 'Win' if current_pnl > 0 else 'Loss',
                        'exit_price': current_stop,
                        'exit_time': current_time,
                        'pnl': entry_price - current_stop if direction == 'Bearish' else current_stop - entry_price,
                        'exit_reason': 'Trailing Stop Hit' if trailing_activated else 'Stop Loss Hit',
                        'max_profit': max_profit,
                        'trailing_activated': trailing_activated
                    }
        
        # End of day exit
        final_price = future_data.iloc[-1]['close']
        final_pnl = final_price - entry_price if direction == 'Bullish' else entry_price - final_price
        
        return {
            'outcome': 'Win' if final_pnl > 0 else 'Loss' if final_pnl < 0 else 'Breakeven',
            'exit_price': final_price,
            'exit_time': future_data.iloc[-1]['datetime'],
            'pnl': final_pnl,
            'exit_reason': 'End of Day',
            'max_profit': max_profit,
            'trailing_activated': trailing_activated
        }
    
    def run_advanced_analysis(self):
        """Run complete advanced analysis"""
        print("\n🚀 Starting Advanced Analysis with New Rules")
        print("="*80)
        
        # Build gap history first
        self.build_gap_history()
        
        # Get unique trading dates
        trading_dates = sorted(self.df['datetime'].dt.date.unique())
        
        print(f"\n📅 Analyzing {len(trading_dates)} trading days with advanced rules...")
        
        advanced_reports = []
        total_trades = 0
        winning_trades = 0
        total_pnl = 0
        
        for i, date in enumerate(trading_dates):
            daily_report = self.analyze_single_day_advanced(date)
            
            if daily_report:
                advanced_reports.append(daily_report)
                
                # Update totals
                for trade in daily_report['trades_taken']:
                    total_trades += 1
                    if trade['result']['outcome'] == 'Win':
                        winning_trades += 1
                    total_pnl += trade['result']['pnl']
                
                # Print progress
                if (i + 1) % 20 == 0:
                    print(f"📊 Processed {i + 1}/{len(trading_dates)} days...")
        
        print(f"✅ Advanced Analysis Complete!")
        print(f"📊 Total Trades: {total_trades}")
        print(f"🎯 Win Rate: {(winning_trades/total_trades*100):.1f}%" if total_trades > 0 else "N/A")
        print(f"💰 Total P&L: {total_pnl:+.1f} points")
        
        return advanced_reports

def main():
    """Run advanced analysis"""
    try:
        analyzer = AdvancedCustomAnalyzer('Nifty.csv')
        reports = analyzer.run_advanced_analysis()
        
        print(f"\n🎉 Advanced analysis complete with {len(reports)} days analyzed!")
        print("💡 Your new rules are now integrated and tested!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
